/**
 * Advanced Image Scaling System
 *
 * This module provides optimized image scaling functionality with various quality/size
 * balancing options. It's designed to integrate with the unified image service.
 */

import { wasmImageProcessor, WasmImageProcessorOptions } from '@/wasm/wasmImageProcessor';
import { performanceMonitor } from '@/utils/optimizations';
import {
  imageCache,
  cacheImage,
  getCachedImage,
  isImageCached
} from './image-cache';
import { simpleHash } from './image-utils';

// Define scaling quality presets
export enum ScalingQuality {
  LOW = 'low',        // Prioritize performance/size
  MEDIUM = 'medium',  // Balanced approach
  HIGH = 'high',      // Prioritize quality
  ULTRA = 'ultra'     // Maximum quality
}

// Scaling options interface
export interface ImageScalingOptions {
  // Dimension constraints (provide at least one)
  maxWidth?: number;
  maxHeight?: number;
  targetWidth?: number;
  targetHeight?: number;

  // Format/quality options
  quality?: number; // 0-1 range
  outputFormat?: string;
  maintainAspectRatio?: boolean;

  // Performance and quality options
  scalingQuality?: ScalingQuality;
  useGPU?: boolean;
  forceResize?: boolean; // Force resize even if image is smaller
  preserveMetadata?: boolean;

  // Special options
  preventUpscaling?: boolean; // Don't upscale smaller images
  optimizationLevel?: number; // 0-3, higher means more optimization
}

// Scaling result interface
export interface ScaledImageResult {
  width: number;
  height: number;
  buffer: ArrayBuffer;
  url?: string;
  originalSize: number;
  newSize: number;
  format: string;
  processingTime: number;
  compressionRatio: number;
}

/**
 * Image Scaling Service
 * Provides highly optimized image scaling with multiple quality options
 */
export class ImageScalingService {
  private static instance: ImageScalingService;
  private hasGpuSupport: boolean = false;

  constructor() {
    // Check for GPU support
    this.detectGpuSupport().catch(() => {
      this.hasGpuSupport = false;
    });
  }

  /**
   * Detect GPU support for acceleration
   */
  private async detectGpuSupport(): Promise<boolean> {
    try {
      // Check if the WebGPU API is available
      if ('gpu' in navigator) {
        // @ts-ignore - WebGPU API might not be in TypeScript definitions yet
        const adapter = await navigator.gpu?.requestAdapter();
        if (adapter) {
          this.hasGpuSupport = true;
          return true;
        }
      }

      // Fallback check for WebGL 2.0
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2');

      if (gl) {
        const ext = gl.getExtension('WEBGL_compute_shader') ||
                   gl.getExtension('EXT_compute_shader');

        this.hasGpuSupport = !!ext;
        return !!ext;
      }

      return false;
    } catch (error) {
      console.warn('GPU support detection failed:', error);
      return false;
    }
  }

  /**
   * Scale an image with optimized settings
   * @param imageData - Source image data buffer
   * @param sourceFormat - Source image format
   * @param options - Scaling options
   * @returns Promise with scaled image result
   */
  public async scaleImage(
    imageData: ArrayBuffer,
    sourceFormat: string,
    options: ImageScalingOptions = {}
  ): Promise<ScaledImageResult> {
    performanceMonitor.measure('imageScaling', () => {});

    try {
      const originalSize = imageData.byteLength;

      // Generate a cache key for this operation
      const cacheKey = this.generateCacheKey(imageData, sourceFormat, options);

      // Check if result is already cached
      if (isImageCached(cacheKey)) {
        const cachedResult = getCachedImage(cacheKey);
        if (cachedResult && !options.forceResize) {
          console.log('Using cached scaling result');
          return cachedResult as ScaledImageResult;
        }
      }

      // Determine the output format
      const outputFormat = options.outputFormat || sourceFormat;

      // Create WASM processing options
      const wasmOptions = this.createWasmOptions(options);

      // Process the image with WASM
      const processedImage = await wasmImageProcessor.processImage(
        imageData,
        sourceFormat,
        outputFormat,
        wasmOptions
      );

      // Calculate compression ratio
      const newSize = processedImage.buffer.byteLength;
      const compressionRatio = originalSize > 0 ? (originalSize / newSize) : 1;

      // Create the result object
      const result: ScaledImageResult = {
        width: processedImage.width,
        height: processedImage.height,
        buffer: processedImage.buffer,
        originalSize,
        newSize,
        format: outputFormat,
        processingTime: processedImage.processingTime,
        compressionRatio
      };

      // SECURITY FIX: Create data URL instead of blob URL
      const mimeType = this.getMimeTypeFromFormat(outputFormat);
      const blob = new Blob([result.buffer], { type: mimeType });
      result.url = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = () => reject(reader.error);
        reader.readAsDataURL(blob);
      });

      // Cache the result
      cacheImage(cacheKey, result);

      performanceMonitor.measure('imageScaling', () => {});
      return result;
    } catch (error) {
      performanceMonitor.measure('imageScaling', () => {});
      console.error('Image scaling error:', error);
      throw error;
    }
  }

  /**
   * Batch process multiple images for scaling
   * @param images - Array of image data and formats
   * @param options - Scaling options
   * @returns Promise with array of scaled results
   */
  public async scaleBatch(
    images: { data: ArrayBuffer; format: string }[],
    options: ImageScalingOptions = {}
  ): Promise<ScaledImageResult[]> {
    performanceMonitor.measure('batchScaling', () => {});

    try {
      // Process in chunks to avoid memory issues
      const CHUNK_SIZE = 3;
      const results: ScaledImageResult[] = [];

      // Process in chunks
      for (let i = 0; i < images.length; i += CHUNK_SIZE) {
        const chunk = images.slice(i, i + CHUNK_SIZE);

        // Process each chunk in parallel
        const chunkPromises = chunk.map(image =>
          this.scaleImage(image.data, image.format, options)
        );

        // Wait for all processing to complete
        const chunkResults = await Promise.all(chunkPromises);
        results.push(...chunkResults);

        // Give UI thread a chance to update
        await new Promise<void>(resolve => setTimeout(resolve, 0));
      }

      const totalTime = performanceMonitor.measure('batchScaling', () => {});
      console.log(`Batch scaled ${images.length} images in ${totalTime}ms (avg: ${totalTime / images.length}ms per image)`);

      return results;
    } catch (error) {
      performanceMonitor.measure('batchScaling', () => {});
      console.error('Batch scaling error:', error);
      throw error;
    }
  }

  /**
   * Create WebAssembly processor options from scaling options
   * @param options - Scaling options
   * @returns WebAssembly processor options
   */
  private createWasmOptions(options: ImageScalingOptions): WasmImageProcessorOptions {
    // Determine maxDimension based on provided constraints
    let maxDimension: number | undefined;

    if (options.maxWidth && options.maxHeight) {
      maxDimension = Math.max(options.maxWidth, options.maxHeight);
    } else if (options.maxWidth) {
      maxDimension = options.maxWidth;
    } else if (options.maxHeight) {
      maxDimension = options.maxHeight;
    } else if (options.targetWidth || options.targetHeight) {
      // If target dimensions are provided, use them
      maxDimension = Math.max(options.targetWidth || 0, options.targetHeight || 0);
    }

    // Determine quality settings based on the scaling quality preset
    let quality = options.quality || 0.9;
    let optimizationLevel = options.optimizationLevel || 2;

    switch (options.scalingQuality) {
      case ScalingQuality.LOW:
        quality = 0.7;
        optimizationLevel = 3;
        break;
      case ScalingQuality.MEDIUM:
        quality = 0.85;
        optimizationLevel = 2;
        break;
      case ScalingQuality.HIGH:
        quality = 0.93;
        optimizationLevel = 1;
        break;
      case ScalingQuality.ULTRA:
        quality = 0.98;
        optimizationLevel = 0;
        break;
    }

    // Return the WASM options
    return {
      maxDimension,
      quality,
      optimizationLevel,
      useGPU: options.useGPU !== undefined ? options.useGPU : this.hasGpuSupport,
      // Add more options as needed
    };
  }

  /**
   * Generate a cache key for scaling operations
   * @param data - Image data
   * @param format - Source format
   * @param options - Scaling options
   * @returns Unique cache key
   */
  private generateCacheKey(
    data: ArrayBuffer,
    format: string,
    options: ImageScalingOptions
  ): string {
    // Create a simple hash of the data
    const dataHash = simpleHash(data);

    // Collect relevant options for the key
    const keyParts = [
      'scaling',
      format,
      dataHash,
      options.outputFormat || format,
      options.quality || 'default',
      options.maxWidth || 0,
      options.maxHeight || 0,
      options.targetWidth || 0,
      options.targetHeight || 0,
      options.scalingQuality || 'default',
      options.optimizationLevel || 'default'
    ];

    // Join parts with underscores
    return keyParts.join('_');
  }

  /**
   * Get MIME type from format string
   * @param format - Format string
   * @returns MIME type
   */
  private getMimeTypeFromFormat(format: string): string {
    const normalized = format.toLowerCase().replace('.', '');

    switch (normalized) {
      case 'png': return 'image/png';
      case 'jpg':
      case 'jpeg': return 'image/jpeg';
      case 'webp': return 'image/webp';
      case 'gif': return 'image/gif';
      case 'avif': return 'image/avif';
      case 'tiff': return 'image/tiff';
      case 'bmp': return 'image/bmp';
      default: return 'application/octet-stream';
    }
  }

  /**
   * Get the optimal format for a specific use case
   * @param targetSize - Target file size in bytes
   * @param requiresTransparency - Whether transparency is required
   * @returns Recommended format and quality settings
   */
  public getOptimalFormat(
    targetSize: number,
    requiresTransparency: boolean
  ): { format: string; quality: number; optimizationLevel: number } {
    // Default to a reasonable format
    let format = 'png';
    let quality = 0.9;
    let optimizationLevel = 2;

    // Very small target size, use highly compressed WebP
    if (targetSize < 50 * 1024) { // < 50KB
      format = requiresTransparency ? 'webp' : 'webp';
      quality = requiresTransparency ? 0.75 : 0.6;
      optimizationLevel = 3;
    }
    // Medium size
    else if (targetSize < 200 * 1024) { // < 200KB
      format = requiresTransparency ? 'webp' : 'jpeg';
      quality = requiresTransparency ? 0.85 : 0.75;
      optimizationLevel = 2;
    }
    // Larger size with good quality
    else if (targetSize < 500 * 1024) { // < 500KB
      format = requiresTransparency ? 'png' : 'jpeg';
      quality = requiresTransparency ? 0.9 : 0.85;
      optimizationLevel = 1;
    }
    // No significant size constraint, use high quality
    else {
      format = requiresTransparency ? 'png' : 'jpeg';
      quality = requiresTransparency ? 1.0 : 0.93;
      optimizationLevel = 0;
    }

    return { format, quality, optimizationLevel };
  }

/**
 * Get singleton instance
 */
public static getInstance(): ImageScalingService {
  if (!ImageScalingService.instance) {
    ImageScalingService.instance = new ImageScalingService();
  }
  return ImageScalingService.instance;
}
}

// Export singleton instance
export const imageScaling = ImageScalingService.getInstance();