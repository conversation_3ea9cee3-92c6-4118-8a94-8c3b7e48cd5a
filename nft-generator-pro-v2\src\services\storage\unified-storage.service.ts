/**
 * Unified Storage Service
 * 
 * IndexedDB ve localStorage için unified interface
 * - Project data persistence
 * - Rules persistence
 * - UI state persistence
 * - Image cache persistence
 */

// ===== TYPES =====

interface StorageItem {
  id: string;
  data: any;
  timestamp: number;
  version: string;
}

interface Project {
  id: string;
  name: string;
  layers?: any[];
  [key: string]: any;
}

interface Rule {
  id: string;
  name: string;
  enabled: boolean;
  [key: string]: any;
}

// ===== CONFIGURATION =====

const DB_NAME = 'nft-generator-v2';
const DB_VERSION = 1;
const STORES = {
  PROJECTS: 'projects',
  RULES: 'rules',
  UI_STATE: 'ui_state',
  IMAGE_CACHE: 'image_cache'
};

// ===== SERVICE IMPLEMENTATION =====

class UnifiedStorageService {
  private db: IDBDatabase | null = null;
  private initPromise: Promise<void> | null = null;

  constructor() {
    this.initPromise = this.initDB();
  }

  /**
   * Initialize IndexedDB
   */
  private async initDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => {
        console.error('Failed to open IndexedDB:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ IndexedDB initialized successfully');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create object stores
        if (!db.objectStoreNames.contains(STORES.PROJECTS)) {
          db.createObjectStore(STORES.PROJECTS, { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains(STORES.RULES)) {
          const rulesStore = db.createObjectStore(STORES.RULES, { keyPath: 'id' });
          rulesStore.createIndex('projectId', 'projectId', { unique: false });
        }

        if (!db.objectStoreNames.contains(STORES.UI_STATE)) {
          db.createObjectStore(STORES.UI_STATE, { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains(STORES.IMAGE_CACHE)) {
          db.createObjectStore(STORES.IMAGE_CACHE, { keyPath: 'id' });
        }

        console.log('📦 IndexedDB stores created');
      };
    });
  }

  /**
   * Ensure DB is initialized
   */
  private async ensureDB(): Promise<IDBDatabase> {
    if (this.initPromise) {
      await this.initPromise;
    }

    if (!this.db) {
      throw new Error('IndexedDB not initialized');
    }

    return this.db;
  }

  /**
   * Save project
   */
  async saveProject(project: Project): Promise<void> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([STORES.PROJECTS], 'readwrite');
      const store = transaction.objectStore(STORES.PROJECTS);

      const storageItem: StorageItem = {
        id: project.id,
        data: project,
        timestamp: Date.now(),
        version: '2.0'
      };

      await new Promise<void>((resolve, reject) => {
        const request = store.put(storageItem);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      console.log(`💾 Project saved: ${project.name}`);
    } catch (error) {
      console.error('Failed to save project:', error);
      // Fallback to localStorage
      this.saveToLocalStorage(`project_${project.id}`, project);
    }
  }

  /**
   * Load current project (latest)
   */
  async loadCurrentProject(): Promise<Project | null> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([STORES.PROJECTS], 'readonly');
      const store = transaction.objectStore(STORES.PROJECTS);

      return new Promise((resolve, reject) => {
        const request = store.getAll();
        
        request.onsuccess = () => {
          const projects = request.result as StorageItem[];
          if (projects.length === 0) {
            resolve(null);
            return;
          }

          // Get the most recent project
          const latest = projects.reduce((prev, current) => 
            prev.timestamp > current.timestamp ? prev : current
          );

          resolve(latest.data);
        };

        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('Failed to load current project:', error);
      // Fallback to localStorage
      return this.loadFromLocalStorage('current_project');
    }
  }

  /**
   * Save rules for a project
   */
  async saveRules(rules: Rule[], projectId: string): Promise<void> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([STORES.RULES], 'readwrite');
      const store = transaction.objectStore(STORES.RULES);

      // Clear existing rules for this project
      const index = store.index('projectId');
      const deleteRequest = index.openCursor(IDBKeyRange.only(projectId));
      
      await new Promise<void>((resolve, reject) => {
        deleteRequest.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;
          if (cursor) {
            cursor.delete();
            cursor.continue();
          } else {
            resolve();
          }
        };
        deleteRequest.onerror = () => reject(deleteRequest.error);
      });

      // Save new rules
      for (const rule of rules) {
        const storageItem: StorageItem = {
          id: `${projectId}_${rule.id}`,
          data: { ...rule, projectId },
          timestamp: Date.now(),
          version: '2.0'
        };

        await new Promise<void>((resolve, reject) => {
          const request = store.put(storageItem);
          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        });
      }

      console.log(`💾 Rules saved for project: ${projectId} (${rules.length} rules)`);
    } catch (error) {
      console.error('Failed to save rules:', error);
      // Fallback to localStorage
      this.saveToLocalStorage(`rules_${projectId}`, rules);
    }
  }

  /**
   * Load rules for a project
   */
  async loadRules(projectId: string): Promise<Rule[]> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([STORES.RULES], 'readonly');
      const store = transaction.objectStore(STORES.RULES);
      const index = store.index('projectId');

      return new Promise((resolve, reject) => {
        const request = index.getAll(projectId);
        
        request.onsuccess = () => {
          const items = request.result as StorageItem[];
          const rules = items.map(item => item.data);
          resolve(rules);
        };

        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('Failed to load rules:', error);
      // Fallback to localStorage
      return this.loadFromLocalStorage(`rules_${projectId}`) || [];
    }
  }

  /**
   * Save generic item
   */
  async saveItem(storeName: string, data: any, id: string): Promise<void> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);

      const storageItem: StorageItem = {
        id,
        data,
        timestamp: Date.now(),
        version: '2.0'
      };

      await new Promise<void>((resolve, reject) => {
        const request = store.put(storageItem);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error(`Failed to save item to ${storeName}:`, error);
      // Fallback to localStorage
      this.saveToLocalStorage(`${storeName}_${id}`, data);
    }
  }

  /**
   * Get generic item
   */
  async getItem(storeName: string, id: string): Promise<any> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);

      return new Promise((resolve, reject) => {
        const request = store.get(id);
        
        request.onsuccess = () => {
          const item = request.result as StorageItem;
          resolve(item ? item.data : null);
        };

        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error(`Failed to get item from ${storeName}:`, error);
      // Fallback to localStorage
      return this.loadFromLocalStorage(`${storeName}_${id}`);
    }
  }

  /**
   * LocalStorage fallback methods
   */
  private saveToLocalStorage(key: string, data: any): void {
    try {
      localStorage.setItem(key, JSON.stringify({
        data,
        timestamp: Date.now(),
        version: '2.0'
      }));
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
    }
  }

  private loadFromLocalStorage(key: string): any {
    try {
      const item = localStorage.getItem(key);
      if (!item) return null;
      
      const parsed = JSON.parse(item);
      return parsed.data;
    } catch (error) {
      console.error('Failed to load from localStorage:', error);
      return null;
    }
  }

  /**
   * Clear all data
   */
  async clearAll(): Promise<void> {
    try {
      const db = await this.ensureDB();
      const storeNames = Object.values(STORES);
      
      for (const storeName of storeNames) {
        const transaction = db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);
        await new Promise<void>((resolve, reject) => {
          const request = store.clear();
          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        });
      }

      // Clear localStorage fallbacks
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('project_') || key.startsWith('rules_') || 
            key.startsWith('ui_state_') || key.startsWith('image_cache_')) {
          localStorage.removeItem(key);
        }
      });

      console.log('🗑️ All storage cleared');
    } catch (error) {
      console.error('Failed to clear storage:', error);
    }
  }
}

// ===== SINGLETON INSTANCE =====

export const unifiedStorageService = new UnifiedStorageService();

export default unifiedStorageService;
