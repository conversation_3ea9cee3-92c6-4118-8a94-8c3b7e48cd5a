import React, { useState, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Box,
  Paper,
  FormHelperText,
  Divider,
  ListItemIcon,
  ListItemButton,
  Tooltip,
  useTheme,
  Chip,
  Alert,
  Badge,
  Switch,
  FormControlLabel,
  Collapse,
  CircularProgress,
  Radio,
  RadioGroup,
  Checkbox,
} from '@mui/material';
import { formStyles } from '@/styles/formStyles';
import StyledFormControl from '@/components/common/StyledFormControl';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Close as CloseIcon,
  Rule as RuleIcon,
  Save as SaveIcon,
  Flag as FlagIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { v4 as uuidv4 } from 'uuid';
import { Rule, RuleCondition, RuleType, LogicalOperator, RulePriority } from '@/types';
import { useNFT } from '@/contexts/NFTContext';
import {
  validateRuleStructure,
  getOrderFromPriority,
  getPriorityFromOrder
} from '@/utils/ruleUtils';
import {
  validateRule,
  findRuleConflicts
} from '@/utils/rules/ruleProcessor';

interface RulesDialogProps {
  open: boolean;
  onClose: () => void;
  rules: Rule[];
  onRulesChange: (rules: Rule[]) => void;
}

/**
 * Rule editing form
 */
interface RuleFormValues {
  id: string;
  name: string;
  description: string;
  type: RuleType;
  conditions: RuleCondition[];
  order: number;
  priority?: RulePriority;
  groupLayers?: string[];
  groupTraits?: {layerId: string, traitIds: string[]}[];
  groupBehavior?: 'sync' | 'reference';
  enabled?: boolean;
}

/**
 * Initial form values
 */
const initialFormValues: RuleFormValues = {
  id: '',
  name: '',
  description: '',
  type: 'IF_THEN',
  conditions: [],
  order: 1,
  priority: 'medium',
  groupLayers: [],
  enabled: true,
};

/**
 * Initial form values for layer group rules
 */
const initialLayerGroupFormValues: RuleFormValues = {
  id: '',
  name: '',
  description: '',
  type: 'LAYER_GROUP',
  conditions: [],
  order: 1,
  priority: 'medium',
  groupLayers: [],
  groupBehavior: 'sync',
  enabled: true,
};

/**
 * Initial form values for trait group rules
 */
const initialTraitGroupFormValues: RuleFormValues = {
  id: '',
  name: '',
  description: '',
  type: 'TRAIT_GROUP',
  conditions: [],
  order: 1,
  priority: 'medium',
  groupTraits: [],
  groupBehavior: 'sync',
  enabled: true,
};

// Priority configuration for UI
const priorityConfig = {
  highest: { color: 'error', label: 'Highest', order: 1 },
  high: { color: 'warning', label: 'High', order: 10 },
  medium: { color: 'primary', label: 'Medium', order: 50 },
  low: { color: 'info', label: 'Low', order: 100 },
  lowest: { color: 'default', label: 'Lowest', order: 500 },
};

/**
 * Rules dialog component
 */
const RulesDialog: React.FC<RulesDialogProps> = ({
  open,
  onClose,
  rules,
  onRulesChange
}) => {
  const { state } = useNFT();
  const { layers } = state;

  useEffect(() => {
    // Debug log to confirm layers are available
    console.log(`RulesDialog: Loaded ${layers?.length || 0} layers from NFT context`);
  }, [layers]);

  // State
  const [showForm, setShowForm] = useState(false);
  const [editingRuleId, setEditingRuleId] = useState<string | null>(null);
  const [formValues, setFormValues] = useState<RuleFormValues>(initialFormValues);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // State for tracking IF and THEN conditions separately
  const [ifConditions, setIfConditions] = useState<RuleCondition[]>([]);
  const [thenConditions, setThenConditions] = useState<RuleCondition[]>([]);

  // Tab state for Trait Rules, Layer Group Rules and Trait Group Rules
  const [activeTab, setActiveTab] = useState<'trait' | 'layer-group' | 'trait-group'>('trait');

  // State for layer group rules
  const [groupLayers, setGroupLayers] = useState<string[]>([]);

  // State for trait group rules
  const [groupTraits, setGroupTraits] = useState<{layerId: string, traitIds: string[]}[]>([]);

  // State for conflict detection
  const [conflicts, setConflicts] = useState<Array<{
    severity: 'error' | 'warning';
    message: string;
    ruleIds: string[];
    affectedLayers?: string[];
  }>>([]);

  // Loading state for conflict detection
  const [isCheckingConflicts, setIsCheckingConflicts] = useState(false);

  // Display state for conflicts
  const [showConflicts, setShowConflicts] = useState(false);

  // Validation warnings
  const [validationWarnings, setValidationWarnings] = useState<string[]>([]);

  /**
   * Helper function to get layer name
   * @param layerId Layer ID
   * @returns Layer name or 'Unknown Layer'
   */
  const getLayerName = (layerId: string | null | undefined): string => {
    if (!layerId) return 'Unknown Layer';

    if (!layers || !Array.isArray(layers)) {
      console.warn('getLayerName: layers is not available or not an array');
      return 'Unknown Layer';
    }

    const layer = layers.find(l => l.id === layerId);
    return layer ? layer.name : 'Unknown Layer';
  };

  /**
   * Helper function to get trait name
   * @param layerId Layer ID
   * @param traitId Trait ID
   * @returns Trait name or 'Unknown Trait'
   */
  const getTraitName = (layerId: string | null | undefined, traitId: string | null | undefined): string => {
    if (!layerId) return 'Unknown Trait';

    // If trait ID is empty or "ALL_TRAITS", return "Any Trait"
    if (!traitId || traitId === '' || traitId === 'ALL_TRAITS') return 'Any Trait';

    if (!layers || !Array.isArray(layers)) {
      console.warn('getTraitName: layers is not available or not an array');
      return 'Unknown Trait';
    }

    const layer = layers.find(l => l.id === layerId);
    if (!layer) return 'Unknown Trait';
    const trait = layer.traits.find(t => t.id === traitId);
    return trait ? trait.name : 'Unknown Trait';
  };

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setShowForm(false);
      setEditingRuleId(null);
      setFormValues(initialFormValues);
      setIfConditions([]);
      setThenConditions([]);
      setErrors({});
      setValidationWarnings([]);

      // Check for conflicts when dialog opens
      detectConflicts(rules);
    }
  }, [open, rules]);

  /**
   * Detect conflicts between rules
   */
  const detectConflicts = useCallback((currentRules: Rule[]) => {
    setIsCheckingConflicts(true);

    // Use setTimeout to avoid blocking the UI
    setTimeout(() => {
      try {
        const detectedConflicts = findRuleConflicts(currentRules);
        setConflicts(detectedConflicts);

        // Show conflicts automatically if there are error-level conflicts
        if (detectedConflicts.some(c => c.severity === 'error')) {
          setShowConflicts(true);
        }
      } catch (error) {
        console.error('Error detecting conflicts:', error);
      } finally {
        setIsCheckingConflicts(false);
      }
    }, 100);
  }, []);

  /**
   * New rule creation handler
   */
  const handleAddRule = () => {
    setEditingRuleId(null);
    setFormValues({
      ...initialFormValues,
      id: uuidv4(),
      order: getOrderFromPriority('medium'),
      priority: 'medium'
    });

    // Initialize with one IF condition
    setIfConditions([{
      layerId: null,
      traitId: undefined
    }]);

    // Initialize with one THEN condition
    setThenConditions([{
      layerId: null,
      targetLayerId: null,
      targetTraitId: undefined,
      operator: 'MUST_HAVE'
    }]);

    setActiveTab('trait');
    setValidationWarnings([]);
    setShowForm(true);
  };

  /**
   * New group rule creation handler
   */
  const handleAddGroupRule = () => {
    setEditingRuleId(null);

    if (activeTab === 'layer-group') {
      setFormValues({
        ...initialLayerGroupFormValues,
        id: uuidv4(),
        order: getOrderFromPriority('medium'),
        priority: 'medium'
      });

      // Reset group layers
      setGroupLayers([]);
    } else if (activeTab === 'trait-group') {
      setFormValues({
        ...initialTraitGroupFormValues,
        id: uuidv4(),
        order: getOrderFromPriority('medium'),
        priority: 'medium'
      });

      // Reset group traits
      setGroupTraits([]);
    }

    setValidationWarnings([]);
    setShowForm(true);
  };

  /**
   * Rule editing handler
   * @param ruleId Rule ID
   */
  const handleEditRule = (ruleId: string) => {
    // Güvenli kontrol
    if (!rules || !Array.isArray(rules)) {
      console.warn('handleEditRule: rules is not available');
      return;
    }

    const rule = rules.find(r => r.id === ruleId);
    if (rule) {
      setEditingRuleId(ruleId);

      try {
        // Deep clone the rule to avoid reference issues
        const clonedRule = JSON.parse(JSON.stringify(rule));

        // Set active tab based on rule type
        if (rule.type === 'LAYER_GROUP') {
          setActiveTab('layer-group');
          setGroupLayers(rule.groupLayers || []);
        } else if (rule.type === 'TRAIT_GROUP') {
          setActiveTab('trait-group');
          setGroupTraits(rule.groupTraits || []);
        } else {
          setActiveTab('trait');

          // For IF_THEN rules, separate IF and THEN conditions
          if (rule.type === 'IF_THEN') {
            // Ensure rule has valid conditions
            if (!rule.conditions || !Array.isArray(rule.conditions)) {
              console.error('Invalid rule conditions:', rule);
              throw new Error('Invalid rule conditions');
            }

            // Extract IF conditions (without targetLayerId)
            const extractedIfConditions = rule.conditions
              .filter(c => c.layerId && !c.targetLayerId)
              .map((c, index) => {
                // Base condition object
                const condition: RuleCondition = {
                  id: uuidv4(),
                  layerId: c.layerId || null,
                  traitId: c.traitId || undefined,
                };

                // For conditions after the first one, normalize logicalOperator
                if (index > 0) {
                  const normalizedOp = c.logicalOperator === 'OR' ? 'OR' : 'AND';
                  console.log(`Loading rule: Condition #${index+1} has logicalOperator="${normalizedOp}" (original was "${c.logicalOperator}")`);
                  condition.logicalOperator = normalizedOp as LogicalOperator;
                }

                return condition;
              });

            // Extract THEN conditions (with targetLayerId)
            const extractedThenConditions = rule.conditions
              .filter(c => c.targetLayerId)
              .map(c => ({
                layerId: null,
                targetLayerId: c.targetLayerId || null,
                targetTraitId: c.targetTraitId === 'ALL_TRAITS' ? undefined : c.targetTraitId || undefined,
                operator: c.operator || 'MUST_HAVE'
              }));

            // If no IF conditions were found, create a default one
            if (extractedIfConditions.length === 0) {
              extractedIfConditions.push({
                layerId: null,
                traitId: undefined
              });
            }

            // If no THEN conditions were found, create a default one
            if (extractedThenConditions.length === 0) {
              extractedThenConditions.push({
                layerId: null,
                targetLayerId: null,
                targetTraitId: undefined,
                operator: 'MUST_HAVE'
              });
            }

            setIfConditions(extractedIfConditions);
            setThenConditions(extractedThenConditions);
          }
        }

        // Set priority if available, otherwise derive from order
        const rulePriority = clonedRule.priority || getPriorityFromOrder(clonedRule.order || 1);

        console.log('Editing rule with restructured form:', clonedRule);
        setFormValues({
          id: clonedRule.id,
          name: clonedRule.name,
          description: clonedRule.description || '',
          type: clonedRule.type || 'IF_THEN',
          conditions: [], // Will be regenerated from ifConditions and thenConditions
          order: clonedRule.order || getOrderFromPriority(rulePriority),
          priority: rulePriority,
          groupLayers: clonedRule.groupLayers || [],
          groupBehavior: clonedRule.groupBehavior || 'sync',
          enabled: clonedRule.enabled !== false // Default to true if not explicitly set to false
        });

        // Pre-validate the rule to show any warnings
        if (rule.type === 'IF_THEN') {
          const validationResult = validateRule(rule);
          if (validationResult.warnings && validationResult.warnings.length > 0) {
            setValidationWarnings(validationResult.warnings);
          } else {
            setValidationWarnings([]);
          }
        } else {
          setValidationWarnings([]);
        }

        setShowForm(true);

      } catch (error) {
        console.error('Error preparing rule for editing:', error);
        // Fallback to a new rule
        setFormValues({
          ...initialFormValues,
          id: rule.id,
          name: rule.name || 'Unnamed Rule',
          description: rule.description || '',
          type: rule.type || 'IF_THEN',
          order: rule.order || 1,
          priority: rule.priority || 'medium',
          conditions: [], // Will be regenerated
          enabled: rule.enabled !== false
        });

        // Initialize with default conditions
        setIfConditions([{
          layerId: null,
          traitId: undefined
        }]);

        setThenConditions([{
          layerId: null,
          targetLayerId: null,
          targetTraitId: undefined,
          operator: 'MUST_HAVE'
        }]);

        setShowForm(true);
      }
    }
  };

  /**
   * Rule deletion handler
   * @param ruleId Rule ID
   */
  const handleDeleteRule = (ruleId: string) => {
    // Güvenli kontrol
    if (!rules || !Array.isArray(rules)) {
      console.warn('handleDeleteRule: rules is not available');
      return;
    }

    const updatedRules = rules.filter(r => r.id !== ruleId);
    onRulesChange(updatedRules);

    // Recheck conflicts after deletion
    detectConflicts(updatedRules);
  };

  /**
   * Form change handler
   * @param field Field name
   * @param value Value
   */
  const handleFormChange = (field: keyof RuleFormValues, value: any) => {
    setFormValues(prev => {
      const updated = {
        ...prev,
        [field]: value
      };

      // Update order when priority changes
      if (field === 'priority' && typeof value === 'string') {
        updated.order = getOrderFromPriority(value);
      }

      return updated;
    });

    // Clear errors
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  /**
   * Add a new IF condition
   */
  const handleAddIfCondition = () => {
    const newCondition: RuleCondition = {
      layerId: null,
      traitId: undefined,
      logicalOperator: 'AND' // Default logical operator
    };

    setIfConditions(prev => [...prev, newCondition]);
  };

  /**
   * Add a new THEN condition
   */
  const handleAddThenCondition = () => {
    const newCondition: RuleCondition = {
      layerId: null,
      targetLayerId: null,
      targetTraitId: undefined,
      operator: 'MUST_HAVE'
    };

    setThenConditions(prev => [...prev, newCondition]);
  };

  /**
   * IF condition change handler
   * @param index Condition index
   * @param field Field name
   * @param value Value
   */
  const handleIfConditionChange = (index: number, field: keyof RuleCondition, value: any) => {
    setIfConditions(prev => {
      const newConditions = [...prev];

      if (field === 'logicalOperator') {
        // Sadece geçerli operatörleri kabul et
        const validOperator = value === 'OR' ? 'OR' : 'AND';

        newConditions[index] = {
          ...newConditions[index],
          logicalOperator: validOperator
        };

        // Değişikliği logla
        console.log(`Condition #${index} operator changed to: ${validOperator}`);
      } else {
        newConditions[index] = {
          ...newConditions[index],
          [field]: value
        };
      }

      return newConditions;
    });
  };

  /**
   * THEN condition change handler
   * @param index Condition index
   * @param field Field name
   * @param value Value
   */
  const handleThenConditionChange = (index: number, field: keyof RuleCondition, value: any) => {
    setThenConditions(prev => {
      const newConditions = [...prev];

      // Update valid condition
      newConditions[index] = {
        ...newConditions[index],
        [field]: value
      };

      // Log changes to help with debugging
      if (field === 'targetLayerId') {
        console.log(`Target Layer ID changed to: ${value} for THEN condition ${index}`);
      }

      if (field === 'targetTraitId') {
        console.log(`Target Trait ID changed to: ${value} for THEN condition ${index}`);
      }

      // If field is "operator", ensure value is correctly saved
      if (field === 'operator') {
        console.log(`Operator changed to: ${value} for THEN condition ${index}`);
      }

      return newConditions;
    });
  };

  /**
   * Delete an IF condition
   * @param index Condition index
   */
  const handleDeleteIfCondition = (index: number) => {
    if (ifConditions.length <= 1) {
      // Keep at least one IF condition
      return;
    }

    setIfConditions(prev => {
      const newConditions = [...prev];
      newConditions.splice(index, 1);
      return newConditions;
    });
  };

  /**
   * Delete a THEN condition
   * @param index Condition index
   */
  const handleDeleteThenCondition = (index: number) => {
    if (thenConditions.length <= 1) {
      // Keep at least one THEN condition
      return;
    }

    setThenConditions(prev => {
      const newConditions = [...prev];
      newConditions.splice(index, 1);
      return newConditions;
    });
  };



  /**
   * Change rule priority
   * @param ruleId Rule ID
   * @param newPriority New priority
   */
  const handleChangePriority = (ruleId: string, newPriority: RulePriority) => {
    const updatedRules = rules.map(rule => {
      if (rule.id === ruleId) {
        return {
          ...rule,
          priority: newPriority,
          order: getOrderFromPriority(newPriority)
        };
      }
      return rule;
    });

    onRulesChange(updatedRules);
  };



  /**
   * Validate form
   * @returns Whether validation is successful
   */
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formValues.name.trim()) {
      newErrors.name = 'Rule name is required';
    }

    // Validate at least one IF condition
    if (ifConditions.length === 0) {
      newErrors.ifConditions = 'At least one IF condition is required';
    } else if (!ifConditions[0].layerId) {
      newErrors.ifConditions = 'Source layer is required';
    }

    // Validate at least one THEN condition
    if (thenConditions.length === 0) {
      newErrors.thenConditions = 'At least one THEN condition is required';
    } else if (!thenConditions[0].targetLayerId) {
      newErrors.thenConditions = 'Target layer is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  /**
   * Handle adding or removing a layer from group rules
   */
  const handleToggleLayerInGroup = (layerId: string) => {
    setGroupLayers(prevLayers => {
      if (prevLayers.includes(layerId)) {
        return prevLayers.filter(id => id !== layerId);
      } else {
        return [...prevLayers, layerId];
      }
    });
  };

  /**
   * Validate the group rule form
   */
  const validateGroupForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formValues.name.trim()) {
      newErrors.name = 'Rule name is required';
    }

    if (activeTab === 'layer-group') {
      if (!groupLayers.length || groupLayers.length < 2) {
        newErrors.groupLayers = 'Select at least two layers to group';
      }
    } else if (activeTab === 'trait-group') {
      if (!groupTraits.length) {
        newErrors.groupTraits = 'Select at least one trait group';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  /**
   * Handle submitting a group rule form
   */
  const handleGroupFormSubmit = () => {
    if (!validateGroupForm()) return;

    try {
      // Create a fresh copy of form values
      const updatedFormValues = JSON.parse(JSON.stringify(formValues));

      // Set the type based on active tab and add the selected layers/traits
      if (activeTab === 'layer-group') {
        updatedFormValues.type = 'LAYER_GROUP';
        updatedFormValues.groupLayers = groupLayers;
      } else if (activeTab === 'trait-group') {
        updatedFormValues.type = 'TRAIT_GROUP';
        updatedFormValues.groupTraits = groupTraits;
      }

      // Empty conditions array for group rules
      updatedFormValues.conditions = [];

      // Log the structured rule
      console.log('Group rule will be submitted with:', updatedFormValues);

      let updatedRules: Rule[];

      if (editingRuleId) {
        // Update existing rule
        updatedRules = (rules || []).map(rule =>
          rule.id === editingRuleId ? updatedFormValues : rule
        );
      } else {
        // Add new rule with unique ID
        if (!updatedFormValues.id) {
          updatedFormValues.id = uuidv4();
        }
        updatedRules = [...(rules || []), updatedFormValues];
      }

      // Update rules
      onRulesChange(updatedRules);

      // Check for conflicts after saving
      detectConflicts(updatedRules);

      setShowForm(false);
    } catch (error) {
      console.error('Error submitting group rule form:', error);
      alert('There was an error saving the rule. Please try again.');
    }
  };

  /**
   * Form submission handler
   */
  const handleFormSubmit = () => {
    // For group rules, use a different handler
    if (activeTab === 'layer-group' || activeTab === 'trait-group') {
      return handleGroupFormSubmit();
    }

    if (!validateForm()) return;

    try {
      // Create a fresh copy of form values
      const updatedFormValues = JSON.parse(JSON.stringify(formValues));

      // Debug log the IF conditions before combining
      console.log('IF conditions before combining:', JSON.stringify(ifConditions));

      // Combine IF and THEN conditions into one array
      const allConditions = [
        ...ifConditions.map((c, index) => {
          // Create clean condition object
          const conditionObj: RuleCondition = {
            id: uuidv4(),
            layerId: c.layerId,
            traitId: c.traitId || null,
          };

          // Add logicalOperator for conditions after the first one
          if (index > 0) {
            // Normalize to just 'OR' or 'AND'
            const normalizedLogicalOp = c.logicalOperator === 'OR' ? 'OR' : 'AND';
            console.log(`Setting condition #${index+1} logicalOperator to "${normalizedLogicalOp}"`);

            // Add to condition object
            conditionObj.logicalOperator = normalizedLogicalOp as LogicalOperator;
          }

          return conditionObj;
        }),
        ...thenConditions.map(c => {
          const conditionObj: RuleCondition = {
            id: uuidv4(),
            layerId: null,
            targetLayerId: c.targetLayerId,
            targetTraitId: c.targetTraitId || null,
            operator: c.operator || 'MUST_HAVE'
          };
          return conditionObj;
        })
      ];

      // Debug: Log each condition
      console.log('Composed conditions:', JSON.stringify(allConditions, null, 2));

      // Handle special case for "ALL_TRAITS" when CANNOT_HAVE is selected
      allConditions.forEach(c => {
        // Check if this is a THEN condition with the CANNOT_HAVE operator
        if ('operator' in c && c.operator === 'CANNOT_HAVE' && !c.targetTraitId) {
          console.log('Setting targetTraitId to ALL_TRAITS for CANNOT_HAVE rule');
          c.targetTraitId = 'ALL_TRAITS';
        }
      });

      // Update conditions array with properly structured conditions
      updatedFormValues.conditions = allConditions;

      // Log the structured rule
      console.log('Form will be submitted with:', updatedFormValues);

      let updatedRules: Rule[];

      if (editingRuleId) {
        // Update existing rule
        updatedRules = (rules || []).map(rule =>
          rule.id === editingRuleId ? updatedFormValues : rule
        );
      } else {
        // Add new rule with unique ID
        if (!updatedFormValues.id) {
          updatedFormValues.id = uuidv4();
        }
        updatedRules = [...(rules || []), updatedFormValues];
      }

      // Run each rule through validateRuleStructure before saving
      const validatedRules = updatedRules.map(rule => {
        // Only validate IF_THEN rules
        if (rule.type === 'IF_THEN') {
          return validateRuleStructure(rule);
        }
        return rule;
      });

      // Debug: Log validated rules
      console.log('Validated rules:', JSON.stringify(validatedRules, null, 2));

      // Update rules
      onRulesChange(validatedRules);

      // Check for conflicts after saving
      detectConflicts(validatedRules);

      setShowForm(false);
    } catch (error) {
      console.error('Error submitting rule form:', error);
      alert('There was an error saving the rule. Please try again.');
    }
  };

  /**
   * Get layer order value
   * @param layerId Layer ID
   * @returns Order value or -1 if not found
   */
  const getLayerOrder = (layerId: string | null | undefined): number => {
    if (!layerId) return -1;

    // Check if layers are available
    if (!layers || !Array.isArray(layers)) {
      console.warn('getLayerOrder: layers is not available or not an array');
      return -1;
    }

    const layer = layers.find(l => l.id === layerId);
    if (!layer) {
      console.warn(`getLayerOrder: layer with ID ${layerId} not found`);
      return -1;
    }

    return layer.order;
  };

  /**
   * Get traits for a specific layer
   * @param layerId Layer ID
   * @returns Traits
   */
  const getTraitsForLayer = (layerId: string | null | undefined) => {
    if (!layerId) return [];

    // Check if layers are available
    if (!layers || !Array.isArray(layers)) {
      console.warn('getTraitsForLayer: layers is not available or not an array');
      return [];
    }

    const layer = layers.find(l => l.id === layerId);
    if (!layer) {
      console.warn(`getTraitsForLayer: layer with ID ${layerId} not found`);
      return [];
    }

    if (!layer.traits || !Array.isArray(layer.traits)) {
      console.warn(`getTraitsForLayer: traits for layer ${layer.name} (${layerId}) are not available or not an array`);
      return [];
    }

    console.log(`getTraitsForLayer: Found ${layer.traits.length} traits for layer ${layer.name}`);
    return layer.traits;
  };

  /**
   * Filter layers based on selection context
   * @param sourceLayerId Layer ID of the source selection (IF part)
   * @returns Filtered layers based on context
   */
  const getFilteredLayersForTarget = (sourceLayerId: string | null | undefined) => {
    // If no source layer selected, return all layers
    if (!sourceLayerId) return layers || [];

    // Get the order value of the source layer
    const sourceOrder = getLayerOrder(sourceLayerId);
    if (sourceOrder < 0) return layers || [];

    // Filter to only show layers that are above the selected IF layer
    // (LOWER order value means the layer is rendered above in the stack)
    return (layers || []).filter(layer => layer.order < sourceOrder);
  };

  /**
   * Cancel handler
   */
  const handleCancel = () => {
    if (showForm) {
      setShowForm(false);
      // Reset group layers/traits if we were editing a group rule
      if (activeTab === 'layer-group') {
        setGroupLayers([]);
      } else if (activeTab === 'trait-group') {
        setGroupTraits([]);
      }
    } else {
      onClose();
    }
  };

  /**
   * Get count of rules with conflicts
   */
  const getConflictingRuleCount = () => {
    const uniqueRuleIds = new Set<string>();
    conflicts.forEach(conflict => {
      conflict.ruleIds.forEach(id => uniqueRuleIds.add(id));
    });
    return uniqueRuleIds.size;
  };

  /**
   * Check if a rule has conflicts
   * @param ruleId Rule ID
   * @returns Conflict info if found
   */
  const getRuleConflicts = (ruleId: string) => {
    return conflicts.filter(conflict => conflict.ruleIds.includes(ruleId));
  };

  /**
   * Check if a rule has error-level conflicts
   * @param ruleId Rule ID
   * @returns Whether the rule has error-level conflicts
   */
  const hasErrorConflicts = (ruleId: string) => {
    return conflicts.some(conflict =>
      conflict.severity === 'error' && conflict.ruleIds.includes(ruleId)
    );
  };

  /**
   * Check if a rule has warning-level conflicts
   * @param ruleId Rule ID
   * @returns Whether the rule has warning-level conflicts
   */
  const hasWarningConflicts = (ruleId: string) => {
    return conflicts.some(conflict =>
      conflict.severity === 'warning' && conflict.ruleIds.includes(ruleId)
    );
  };

  // Theme for styling
  const theme = useTheme();

  // Get a summary of a rule for displaying in the sidebar
  const getRuleSummary = (rule: Rule): string => {
    // For LAYER_GROUP rules, show the grouped layers
    if (rule.type === 'LAYER_GROUP') {
      if (!rule.groupLayers || rule.groupLayers.length < 2) {
        return "Incomplete layer group rule";
      }

      // Get the layer names
      const layerNames = rule.groupLayers.map(layerId => getLayerName(layerId));

      // Create a summary with all layer names
      return `Layer Group: ${layerNames.join(', ')}`;
    }

    // For TRAIT_GROUP rules, show the grouped traits
    if (rule.type === 'TRAIT_GROUP') {
      if (!rule.groupTraits || rule.groupTraits.length < 1) {
        return "Incomplete trait group rule";
      }

      // Get the trait names
      const traitSummary = rule.groupTraits.map(group => {
        const layerName = getLayerName(group.layerId);
        const traitCount = group.traitIds.length;
        return `${layerName} (${traitCount} traits)`;
      });

      // Create a summary with all trait groups
      return `Trait Group: ${traitSummary.join(', ')}`;
    }

    // For IF_THEN rules
    if (!rule.conditions || rule.conditions.length < 2) {
      return "Incomplete rule";
    }

    // Find IF conditions (without targetLayerId)
    const ifConditions = rule.conditions.filter(c => c.layerId && !c.targetLayerId);
    // Find THEN conditions (with targetLayerId)
    const thenConditions = rule.conditions.filter(c => c.targetLayerId);

    if (ifConditions.length === 0 || thenConditions.length === 0) {
      return "Incomplete rule";
    }

    // First IF condition summary
    const firstLayerName = getLayerName(ifConditions[0].layerId);
    const firstTraitName = getTraitName(ifConditions[0].layerId, ifConditions[0].traitId);

    // Create summary for all IF conditions
    let summary = `If ${firstLayerName} has ${firstTraitName}`;

    // Add additional IF conditions if any
    for (let i = 1; i < ifConditions.length; i++) {
      const logicalOp = ifConditions[i].logicalOperator === 'OR' ? 'OR' : 'AND';
      const layerName = getLayerName(ifConditions[i].layerId);
      const traitName = getTraitName(ifConditions[i].layerId, ifConditions[i].traitId);
      summary += ` ${logicalOp} ${layerName} has ${traitName}`;
    }

    // Get first THEN condition
    const firstThenCondition = thenConditions[0];
    const targetLayerName = getLayerName(firstThenCondition.targetLayerId);
    const targetTraitName = getTraitName(firstThenCondition.targetLayerId, firstThenCondition.targetTraitId);
    const operatorText = firstThenCondition.operator === 'CANNOT_HAVE' ? 'cannot have' : 'must have';

    summary += `, then ${targetLayerName} ${operatorText} ${targetTraitName}`;

    // If there are more THEN conditions, add a note
    if (thenConditions.length > 1) {
      summary += ` (+ ${thenConditions.length - 1} more conditions)`;
    }

    return summary;
  };

  // Get sorted rules for the current tab
  const getSortedRulesForTab = () => {
    // Güvenli kontrol - rules undefined olabilir
    if (!rules || !Array.isArray(rules)) {
      return [];
    }

    if (activeTab === 'trait') {
      return rules
        .filter(rule => rule.type === 'IF_THEN')
        .sort((a, b) => (a.order || 0) - (b.order || 0));
    } else if (activeTab === 'layer-group') {
      return rules
        .filter(rule => rule.type === 'LAYER_GROUP')
        .sort((a, b) => (a.order || 0) - (b.order || 0));
    } else if (activeTab === 'trait-group') {
      return rules
        .filter(rule => rule.type === 'TRAIT_GROUP')
        .sort((a, b) => (a.order || 0) - (b.order || 0));
    }
    return [];
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          width: '80%',
          height: '80%',
          maxWidth: 'unset',
          maxHeight: 'unset',
          display: 'flex',
          flexDirection: 'column',
        }
      }}
    >
      <DialogTitle sx={{ p: 1.5, pb: 1.5, m: 0 }}>
        Compatibility Rules
        <IconButton
          onClick={onClose}
          sx={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      {/* Conflict Detection Banner */}
      {conflicts.length > 0 && (
        <Box
          sx={{
            px: 1.5,
            py: 1,
            backgroundColor: conflicts.some(c => c.severity === 'error')
              ? theme.palette.error.light
              : theme.palette.warning.light,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {conflicts.some(c => c.severity === 'error') ? (
              <ErrorIcon color="error" fontSize="small" />
            ) : (
              <WarningIcon color="warning" fontSize="small" />
            )}
            <Typography variant="body2">
              {conflicts.some(c => c.severity === 'error')
                ? `${getConflictingRuleCount()} rules have conflicts that must be resolved`
                : `${getConflictingRuleCount()} rules have potential conflicts that should be reviewed`}
            </Typography>
          </Box>
          <Button
            size="small"
            variant="outlined"
            color={conflicts.some(c => c.severity === 'error') ? "error" : "warning"}
            onClick={() => setShowConflicts(!showConflicts)}
          >
            {showConflicts ? "Hide Details" : "Show Details"}
          </Button>
        </Box>
      )}

      {/* Conflict Details */}
      <Collapse in={showConflicts && conflicts.length > 0}>
        <Box sx={{ px: 1.5, py: 1, backgroundColor: 'background.paper', borderTop: `1px solid ${theme.palette.divider}` }}>
          <Typography variant="subtitle2" gutterBottom>
            Rule Conflicts
          </Typography>
          <List dense sx={{ maxHeight: '200px', overflow: 'auto' }}>
            {conflicts.map((conflict, index) => (
              <ListItem key={`conflict-${index}`}>
                <ListItemIcon sx={{ minWidth: 36 }}>
                  {conflict.severity === 'error' ? (
                    <ErrorIcon color="error" fontSize="small" />
                  ) : (
                    <WarningIcon color="warning" fontSize="small" />
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={conflict.message}
                  secondary={
                    <Box sx={{ mt: 0.5 }}>
                      <Typography variant="caption" component="div">
                        Affected Rules:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                        {conflict.ruleIds.map(id => {
                          const rule = (rules || []).find(r => r.id === id);
                          return rule ? (
                            <Chip
                              key={id}
                              label={rule.name}
                              size="small"
                              color={conflict.severity === 'error' ? "error" : "warning"}
                              variant="outlined"
                              onClick={() => handleEditRule(id)}
                              sx={{ cursor: 'pointer' }}
                            />
                          ) : null;
                        })}
                      </Box>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </Box>
      </Collapse>

      <DialogContent dividers sx={{ p: 0, display: 'flex', flexDirection: 'column', flexGrow: 1, overflow: 'hidden' }}>
        {/* Main content wrapper - Flex layout */}
        <Box sx={{ display: 'flex', flexGrow: 1, overflow: 'hidden' }}>
          {/* Sidebar - fixed width */}
          <Box sx={{
            width: 300,
            minWidth: 300,
            maxWidth: 300,
            borderRight: `1px solid ${theme.palette.divider}`,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
          }}>
            {/* Sidebar Header with Tabs - reduced padding */}
            <Box sx={{ p: 1.5, borderBottom: `1px solid ${theme.palette.divider}` }}>
              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                <Box sx={{ display: 'flex' }}>
                  <Box
                    sx={{
                      px: 2,
                      py: 1,
                      cursor: 'pointer',
                      borderBottom: '2px solid',
                      borderColor: !showForm && activeTab === 'trait' ? 'primary.main' : 'transparent',
                      color: !showForm && activeTab === 'trait' ? 'primary.main' : 'text.primary',
                      fontWeight: !showForm && activeTab === 'trait' ? 'medium' : 'normal',
                    }}
                    onClick={() => {
                      if (!showForm) {
                        setActiveTab('trait');
                      }
                    }}
                  >
                    Trait Rules
                  </Box>
                  <Box
                    sx={{
                      px: 2,
                      py: 1,
                      cursor: 'pointer',
                      borderBottom: '2px solid',
                      borderColor: !showForm && activeTab === 'layer-group' ? 'primary.main' : 'transparent',
                      color: !showForm && activeTab === 'layer-group' ? 'primary.main' : 'text.primary',
                      fontWeight: !showForm && activeTab === 'layer-group' ? 'medium' : 'normal',
                    }}
                    onClick={() => {
                      if (!showForm) {
                        setActiveTab('layer-group');
                      }
                    }}
                  >
                    Layer Grouping
                  </Box>
                  <Box
                    sx={{
                      px: 2,
                      py: 1,
                      cursor: 'pointer',
                      borderBottom: '2px solid',
                      borderColor: !showForm && activeTab === 'trait-group' ? 'primary.main' : 'transparent',
                      color: !showForm && activeTab === 'trait-group' ? 'primary.main' : 'text.primary',
                      fontWeight: !showForm && activeTab === 'trait-group' ? 'medium' : 'normal',
                    }}
                    onClick={() => {
                      if (!showForm) {
                        setActiveTab('trait-group');
                      }
                    }}
                  >
                    Trait Grouping
                  </Box>
                </Box>
              </Box>

              <Typography variant="subtitle1" gutterBottom>
                {activeTab === 'trait' ? 'Trait Rules List' : 'Group Rules List'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {activeTab === 'trait'
                  ? 'Define rules to control trait combinations.'
                  : 'Group layers to treat them as a single layer for dice rolls.'}
              </Typography>

              {/* Rule Conflicts Check Button */}
              <Box sx={{ mt: 1.5, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Tooltip title="Check for rule conflicts">
                  <Button
                    size="small"
                    variant="outlined"
                    color="primary"
                    startIcon={isCheckingConflicts ? <CircularProgress size={16} /> : <RefreshIcon />}
                    onClick={() => detectConflicts(rules || [])}
                    disabled={isCheckingConflicts}
                  >
                    Check Conflicts
                  </Button>
                </Tooltip>

                <Box>
                  <Typography variant="caption" color="text.secondary">
                    {getSortedRulesForTab().length} rules
                  </Typography>
                </Box>
              </Box>
            </Box>

            {/* Rules List */}
            <Box sx={{ overflowY: 'auto', flexGrow: 1 }}>
              <List>
                {getSortedRulesForTab().map((rule) => {
                  // Get rule conflicts
                  getRuleConflicts(rule.id);
                  const hasError = hasErrorConflicts(rule.id);
                  const hasWarning = hasWarningConflicts(rule.id);

                  return (
                    <ListItem
                      key={rule.id}
                      disablePadding
                      secondaryAction={
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <Tooltip title="Change priority">
                            <IconButton
                              edge="end"
                              onClick={(e) => {
                                e.stopPropagation();

                                // Find current priority
                                const currentPriority = rule.priority || getPriorityFromOrder(rule.order || 1);

                                // Get next priority in cycle
                                const priorities: RulePriority[] = ['highest', 'high', 'medium', 'low', 'lowest'];
                                const currentIndex = priorities.indexOf(currentPriority);
                                const nextIndex = (currentIndex + 1) % priorities.length;
                                const nextPriority = priorities[nextIndex];

                                handleChangePriority(rule.id, nextPriority);
                              }}
                              size="small"
                              color="primary"
                            >
                              <FlagIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>

                          <Tooltip title="Delete rule">
                            <IconButton
                              edge="end"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteRule(rule.id);
                              }}
                              size="small"
                              color="error"
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      }
                      sx={{
                        opacity: rule.enabled === false ? 0.5 : 1,
                        backgroundColor: hasError
                          ? theme.palette.error.light
                          : hasWarning
                            ? theme.palette.warning.light
                            : 'transparent'
                      }}
                    >
                      <ListItemButton
                        onClick={() => handleEditRule(rule.id)}
                        selected={editingRuleId === rule.id}
                        sx={{ pr: 6 }}
                      >
                        <ListItemIcon>
                          <Box sx={{ position: 'relative' }}>
                            <RuleIcon color={rule.enabled === false ? "disabled" : "primary"} />
                            {(hasError || hasWarning) && (
                              <Box
                                sx={{
                                  position: 'absolute',
                                  top: -5,
                                  right: -5,
                                  width: 12,
                                  height: 12,
                                  borderRadius: '50%',
                                  backgroundColor: hasError ? 'error.main' : 'warning.main',
                                  border: `1px solid ${theme.palette.background.paper}`
                                }}
                              />
                            )}
                          </Box>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography
                                variant="body2"
                                sx={{
                                  fontWeight: rule.priority === 'highest' || rule.priority === 'high' ? 'bold' : 'normal'
                                }}
                              >
                                {rule.name}
                              </Typography>
                              <Chip
                                label={priorityConfig[rule.priority || 'medium'].label}
                                size="small"
                                color={priorityConfig[rule.priority || 'medium'].color as any}
                                variant="outlined"
                                sx={{ height: 18, '& .MuiChip-label': { px: 0.8, py: 0 } }}
                              />

                              {rule.enabled === false && (
                                <Chip
                                  label="Disabled"
                                  size="small"
                                  color="default"
                                  variant="outlined"
                                  sx={{ height: 18, '& .MuiChip-label': { px: 0.8, py: 0 } }}
                                />
                              )}
                            </Box>
                          }
                          secondary={
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                maxWidth: '180px'
                              }}
                            >
                              {getRuleSummary(rule)}
                            </Typography>
                          }
                        />
                      </ListItemButton>
                    </ListItem>
                  );
                })}
              </List>

              {getSortedRulesForTab().length === 0 && (
                <Box sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    {activeTab === 'trait'
                      ? 'No trait rules defined yet'
                      : activeTab === 'layer-group'
                        ? 'No layer grouping rules defined yet'
                        : 'No trait grouping rules defined yet'}
                  </Typography>
                </Box>
              )}
            </Box>

            {/* Add Rule Button - reduced padding */}
            <Box sx={{ p: 1.5, borderTop: `1px solid ${theme.palette.divider}` }}>
              <Button
                variant="contained"
                fullWidth
                size="medium"
                startIcon={<AddIcon />}
                onClick={activeTab === 'trait' ? handleAddRule : handleAddGroupRule}
              >
                {activeTab === 'trait'
                  ? 'Add Trait Rule'
                  : activeTab === 'layer-group'
                    ? 'Add Layer Group Rule'
                    : 'Add Trait Group Rule'}
              </Button>
            </Box>
          </Box>

          {/* Content Area with scrollable content */}
          <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', height: '100%', overflow: 'hidden' }}>
            <Box sx={{ flexGrow: 1, overflow: 'auto', p: 1.5 }}>
              {showForm ? (
                // Rule Form
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6">
                      {editingRuleId
                        ? `Edit ${activeTab === 'trait'
                            ? 'Trait Rule'
                            : activeTab === 'layer-group'
                              ? 'Layer Group Rule'
                              : 'Trait Group Rule'}`
                        : `Create New ${activeTab === 'trait'
                            ? 'Trait Rule'
                            : activeTab === 'layer-group'
                              ? 'Layer Group Rule'
                              : 'Trait Group Rule'}`}
                    </Typography>
                  </Box>

                  {/* Validation Warnings */}
                  {validationWarnings.length > 0 && (
                    <Alert
                      severity="warning"
                      sx={{ mb: 2 }}
                      variant="outlined"
                    >
                      <Typography variant="subtitle2">Warnings:</Typography>
                      <ul style={{ marginTop: 4, marginBottom: 0, paddingLeft: 20 }}>
                        {validationWarnings.map((warning, index) => (
                          <li key={index}>
                            <Typography variant="body2">{warning}</Typography>
                          </li>
                        ))}
                      </ul>
                    </Alert>
                  )}

                  <Grid container spacing={formStyles.getGridSpacing()}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Rule Name"
                        name="name"
                        value={formValues.name}
                        onChange={(e) => handleFormChange('name', e.target.value)}
                        error={!!errors.name}
                        helperText={errors.name}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Description (optional)"
                        name="description"
                        value={formValues.description}
                        onChange={(e) => handleFormChange('description', e.target.value)}
                      />
                    </Grid>

                    {/* Enabled setting */}
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formValues.enabled !== false}
                            onChange={(e) => handleFormChange('enabled', e.target.checked)}
                            color="primary"
                          />
                        }
                        label="Rule Enabled"
                      />
                      <FormHelperText>
                        Disabled rules are ignored during rule evaluation
                      </FormHelperText>
                    </Grid>
                  </Grid>

                  {activeTab === 'trait' ? (
                    // Trait Rules Form
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Rule Conditions
                      </Typography>

                      {/* Display errors */}
                      {(errors.ifConditions || errors.thenConditions) && (
                        <FormHelperText error sx={{ ml: 1 }}>
                          {errors.ifConditions || errors.thenConditions}
                        </FormHelperText>
                      )}

                      <Box sx={{ backgroundColor: theme.palette.background.default, p: 1.5, borderRadius: 1, mt: 1.5 }}>
                        {/* IF Conditions Section */}
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          mb: 1.5
                        }}>
                          <Typography variant="subtitle2">
                            IF Conditions:
                          </Typography>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<AddIcon />}
                            onClick={handleAddIfCondition}
                          >
                            Add Condition
                          </Button>
                        </Box>

                        {/* Display IF conditions */}
                        {ifConditions.length > 0 ? (
                          <Box>
                            {ifConditions.map((condition, index) => (
                              <React.Fragment key={`if-${index}`}>
                                {/* AND/OR selector between conditions (after the first one) - improved */}
                                {index > 0 && (
                                  <Box sx={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    my: 1
                                  }}>
                                    <Paper
                                      elevation={2}
                                      sx={{
                                        display: 'inline-flex',
                                        overflow: 'hidden',
                                        border: `1px solid ${theme.palette.divider}`,
                                        borderRadius: 1,
                                        zIndex: 10
                                      }}
                                    >
                                      <Button
                                        variant={condition.logicalOperator === 'AND' ? "contained" : "outlined"}
                                        size="small"
                                        sx={{
                                          borderRadius: 0,
                                          minWidth: '60px',
                                          borderRight: `1px solid ${theme.palette.divider}`
                                        }}
                                        onClick={() => {
                                          console.log("Setting to AND");
                                          handleIfConditionChange(index, 'logicalOperator', 'AND');
                                        }}
                                      >
                                        AND
                                      </Button>
                                      <Button
                                        variant={condition.logicalOperator === 'OR' ? "contained" : "outlined"}
                                        size="small"
                                        sx={{
                                          borderRadius: 0,
                                          minWidth: '60px'
                                        }}
                                        onClick={() => {
                                          console.log("Setting to OR");
                                          handleIfConditionChange(index, 'logicalOperator', 'OR');
                                        }}
                                      >
                                        OR
                                      </Button>
                                    </Paper>
                                  </Box>
                                )}

                                <Paper
                                  elevation={1}
                                  sx={{ p: 1.5, mb: 1, borderRadius: 1 }}
                                >
                                  <Grid container spacing={2.5}>
                                    <Grid item xs={12} sm={5}>
                                      <StyledFormControl
                                        label="Layer"
                                        variant="dialog"
                                        size="small"
                                      >
                                        <Select
                                          value={condition.layerId || ''}
                                          label="Layer"
                                          onChange={(e) => handleIfConditionChange(index, 'layerId', e.target.value || null)}
                                        >
                                          <MenuItem value=""><em>Select a Layer</em></MenuItem>
                                          {layers && layers.length > 0 ? (
                                            layers.map((layer) => (
                                              <MenuItem key={layer.id} value={layer.id}>
                                                {layer.name}
                                              </MenuItem>
                                            ))
                                          ) : (
                                            <MenuItem disabled>No layers available</MenuItem>
                                          )}
                                        </Select>
                                      </StyledFormControl>
                                    </Grid>

                                    <Grid item xs={12} sm={5}>
                                      <StyledFormControl
                                        label="Trait"
                                        variant="dialog"
                                        size="small"
                                        disabled={!condition.layerId}
                                      >
                                        <Select
                                          value={condition.traitId || ''}
                                          label="Trait"
                                          onChange={(e) => handleIfConditionChange(index, 'traitId', e.target.value || undefined)}
                                        >
                                          <MenuItem value=""><em>Any Trait</em></MenuItem>
                                          {getTraitsForLayer(condition.layerId).map((trait) => (
                                            <MenuItem key={trait.id} value={trait.id}>
                                              {trait.name}
                                            </MenuItem>
                                          ))}
                                        </Select>
                                      </StyledFormControl>
                                    </Grid>

                                    <Grid item xs={12} sm={2} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                                      {index > 0 && (
                                        <Tooltip title="Remove condition">
                                          <IconButton
                                            color="error"
                                            size="small"
                                            onClick={() => handleDeleteIfCondition(index)}
                                          >
                                            <DeleteIcon fontSize="small" />
                                          </IconButton>
                                        </Tooltip>
                                      )}
                                    </Grid>
                                  </Grid>
                                </Paper>
                              </React.Fragment>
                            ))}
                          </Box>
                        ) : (
                          <Typography color="text.secondary" sx={{ mb: 3 }}>
                            No IF conditions defined. Click the + button to add.
                          </Typography>
                        )}

                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 1.5 }}>
                          <Divider sx={{ flex: 1 }} />
                          <Typography variant="body2" sx={{ mx: 1.5 }}>THEN</Typography>
                          <Divider sx={{ flex: 1 }} />
                        </Box>

                        {/* THEN Conditions Section */}
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          mb: 1.5
                        }}>
                          <Typography variant="subtitle2">
                            THEN Condition:
                          </Typography>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<AddIcon />}
                            onClick={handleAddThenCondition}
                          >
                            Add Condition
                          </Button>
                        </Box>

                        {/* Display THEN conditions */}
                        {thenConditions.length > 0 ? (
                          <Box>
                            {thenConditions.map((condition, index) => (
                              <Paper
                                key={`then-${index}`}
                                elevation={1}
                                sx={{ p: 1.5, mb: 1, borderRadius: 1 }}
                              >
                                <Grid container spacing={2.5}>
                                  <Grid item xs={12} sm={3}>
                                    <StyledFormControl
                                      label="Target Layer"
                                      variant="dialog"
                                      size="small"
                                    >
                                      <Select
                                        value={condition.targetLayerId || ''}
                                        label="Target Layer"
                                        onChange={(e) => handleThenConditionChange(index, 'targetLayerId', e.target.value || null)}
                                      >
                                        <MenuItem value=""><em>Select a Layer</em></MenuItem>
                                        {/* Filter: Show layers ABOVE the selected IF layer */}
                                        {ifConditions.length > 0 && ifConditions[0].layerId ? (
                                          getFilteredLayersForTarget(ifConditions[0].layerId).length > 0 ? (
                                            getFilteredLayersForTarget(ifConditions[0].layerId).map((layer) => (
                                              <MenuItem key={layer.id} value={layer.id}>
                                                {layer.name}
                                              </MenuItem>
                                            ))
                                          ) : (
                                            <MenuItem disabled>No valid target layers below selected source layer</MenuItem>
                                          )
                                        ) : (
                                          // If no IF selection yet, show all layers
                                          layers && layers.length > 0 ? (
                                            layers.map((layer) => (
                                              <MenuItem key={layer.id} value={layer.id}>
                                                {layer.name}
                                              </MenuItem>
                                            ))
                                          ) : (
                                            <MenuItem disabled>No layers available</MenuItem>
                                          )
                                        )}
                                      </Select>
                                    </StyledFormControl>
                                  </Grid>

                                  <Grid item xs={12} sm={3}>
                                    <StyledFormControl
                                      label="Operator"
                                      variant="dialog"
                                      size="small"
                                    >
                                      <Select
                                        value={condition.operator || 'MUST_HAVE'}
                                        label="Operator"
                                        onChange={(e) => handleThenConditionChange(index, 'operator', e.target.value)}
                                      >
                                        <MenuItem value="MUST_HAVE">Must Have</MenuItem>
                                        <MenuItem value="CANNOT_HAVE">Cannot Have</MenuItem>
                                      </Select>
                                    </StyledFormControl>
                                  </Grid>

                                  <Grid item xs={12} sm={4}>
                                    <StyledFormControl
                                      label="Target Trait"
                                      variant="dialog"
                                      size="small"
                                      disabled={!condition.targetLayerId}
                                    >
                                      <Select
                                        value={condition.targetTraitId || ''}
                                        label="Target Trait"
                                        onChange={(e) => handleThenConditionChange(index, 'targetTraitId', e.target.value || undefined)}
                                      >
                                        <MenuItem value=""><em>Any Trait</em></MenuItem>
                                        {getTraitsForLayer(condition.targetLayerId).map((trait) => (
                                          <MenuItem key={trait.id} value={trait.id}>
                                            {trait.name}
                                          </MenuItem>
                                        ))}
                                      </Select>
                                    </StyledFormControl>
                                  </Grid>

                                  <Grid item xs={12} sm={2} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                                    {index > 0 && (
                                      <Tooltip title="Remove condition">
                                        <IconButton
                                          color="error"
                                          size="small"
                                          onClick={() => handleDeleteThenCondition(index)}
                                        >
                                          <DeleteIcon fontSize="small" />
                                        </IconButton>
                                      </Tooltip>
                                    )}
                                  </Grid>
                                </Grid>
                              </Paper>
                            ))}
                          </Box>
                        ) : (
                          <Typography color="text.secondary" sx={{ mb: 3 }}>
                            No THEN condition defined. Click the + button to add.
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  ) : activeTab === 'layer-group' ? (
                    // Layer Group Rules Form
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Layer Grouping
                      </Typography>

                      {/* Display errors */}
                      {errors.groupLayers && (
                        <FormHelperText error sx={{ ml: 1, mb: 1 }}>
                          {errors.groupLayers}
                        </FormHelperText>
                      )}

                      <Box sx={{ backgroundColor: theme.palette.background.default, p: 1.5, borderRadius: 1, mt: 1.5 }}>
                        <Typography variant="body2" color="text.secondary" paragraph>
                          Group rules allow you to treat multiple layers as a single layer for randomization.
                          This is useful for traits that need to be in different layers but should be treated
                          as if they're in the same layer when rolling dice.
                        </Typography>

                        {/* Group Behavior */}
                        <FormControl component="fieldset" sx={{ mb: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Group Behavior:
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 2 }}>
                            <Paper
                              variant={formValues.groupBehavior === 'sync' ? "elevation" : "outlined"}
                              elevation={formValues.groupBehavior === 'sync' ? 3 : 0}
                              sx={{
                                p: 1.5,
                                flex: 1,
                                cursor: 'pointer',
                                borderColor: formValues.groupBehavior === 'sync' ? 'primary.main' : undefined,
                                bgcolor: formValues.groupBehavior === 'sync' ? 'primary.light' : undefined,
                                opacity: formValues.groupBehavior === 'sync' ? 1 : 0.7,
                              }}
                              onClick={() => handleFormChange('groupBehavior', 'sync')}
                            >
                              <Typography variant="subtitle2" color={formValues.groupBehavior === 'sync' ? 'primary' : 'text.primary'}>
                                Sync Values
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                All layers in the group will use the same trait whenever one is changed
                              </Typography>
                            </Paper>

                            <Paper
                              variant={formValues.groupBehavior === 'reference' ? "elevation" : "outlined"}
                              elevation={formValues.groupBehavior === 'reference' ? 3 : 0}
                              sx={{
                                p: 1.5,
                                flex: 1,
                                cursor: 'pointer',
                                borderColor: formValues.groupBehavior === 'reference' ? 'primary.main' : undefined,
                                bgcolor: formValues.groupBehavior === 'reference' ? 'primary.light' : undefined,
                                opacity: formValues.groupBehavior === 'reference' ? 1 : 0.7,
                              }}
                              onClick={() => handleFormChange('groupBehavior', 'reference')}
                            >
                              <Typography variant="subtitle2" color={formValues.groupBehavior === 'reference' ? 'primary' : 'text.primary'}>
                                Reference First
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                First layer with a selection will be used as reference for others
                              </Typography>
                            </Paper>
                          </Box>
                        </FormControl>

                        <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                          Select layers to group together:
                        </Typography>

                        <Grid container spacing={2.5} sx={{ mt: 0.5 }}>
                          {layers && layers.length > 0 ? (
                            layers.map((layer) => (
                              <Grid item xs={12} sm={6} md={4} key={layer.id}>
                                <Paper
                                  variant="outlined"
                                  sx={{
                                    p: 1.5,
                                    bgcolor: groupLayers.includes(layer.id)
                                      ? 'primary.light'
                                      : 'background.paper',
                                    borderColor: groupLayers.includes(layer.id)
                                      ? 'primary.main'
                                      : 'divider',
                                    cursor: 'pointer',
                                    '&:hover': {
                                      borderColor: 'primary.main',
                                      bgcolor: 'action.hover'
                                    }
                                  }}
                                  onClick={() => handleToggleLayerInGroup(layer.id)}
                                >
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'space-between'
                                    }}
                                  >
                                    <Typography variant="body1">
                                      {layer.name}
                                    </Typography>
                                    {groupLayers.includes(layer.id) && (
                                      <Box
                                        sx={{
                                          width: 24,
                                          height: 24,
                                          borderRadius: '50%',
                                          bgcolor: 'primary.main',
                                          display: 'flex',
                                          alignItems: 'center',
                                          justifyContent: 'center',
                                          color: 'primary.contrastText'
                                        }}
                                      >
                                        ✓
                                      </Box>
                                    )}
                                  </Box>
                                  <Typography variant="body2" color="text.secondary">
                                    {layer.traits.length} traits
                                  </Typography>
                                </Paper>
                              </Grid>
                            ))
                          ) : (
                            <Grid item xs={12}>
                              <Typography color="text.secondary">
                                No layers available to group.
                              </Typography>
                            </Grid>
                          )}
                        </Grid>
                      </Box>
                    </Box>
                  ) : (
                    // Trait Group Rules Form
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Trait Grouping
                      </Typography>

                      {/* Display errors */}
                      {errors.groupTraits && (
                        <FormHelperText error sx={{ ml: 1, mb: 1 }}>
                          {errors.groupTraits}
                        </FormHelperText>
                      )}

                      <Box sx={{ backgroundColor: theme.palette.background.default, p: 1.5, borderRadius: 1, mt: 1.5 }}>
                        <Typography variant="body2" color="text.secondary" paragraph>
                          Trait grouping allows you to define groups of traits that should be treated as a single unit.
                          This is useful for traits that need to be synchronized across different layers.
                        </Typography>

                        {/* Group Behavior */}
                        <FormControl component="fieldset" sx={{ mb: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Group Behavior:
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 2 }}>
                            <Paper
                              variant={formValues.groupBehavior === 'sync' ? "elevation" : "outlined"}
                              elevation={formValues.groupBehavior === 'sync' ? 3 : 0}
                              sx={{
                                p: 1.5,
                                flex: 1,
                                cursor: 'pointer',
                                borderColor: formValues.groupBehavior === 'sync' ? 'primary.main' : undefined,
                                bgcolor: formValues.groupBehavior === 'sync' ? 'primary.light' : undefined,
                                opacity: formValues.groupBehavior === 'sync' ? 1 : 0.7,
                              }}
                              onClick={() => handleFormChange('groupBehavior', 'sync')}
                            >
                              <Typography variant="subtitle2" color={formValues.groupBehavior === 'sync' ? 'primary' : 'text.primary'}>
                                Sync Values
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                All traits in the group will be selected together
                              </Typography>
                            </Paper>

                            <Paper
                              variant={formValues.groupBehavior === 'reference' ? "elevation" : "outlined"}
                              elevation={formValues.groupBehavior === 'reference' ? 3 : 0}
                              sx={{
                                p: 1.5,
                                flex: 1,
                                cursor: 'pointer',
                                borderColor: formValues.groupBehavior === 'reference' ? 'primary.main' : undefined,
                                bgcolor: formValues.groupBehavior === 'reference' ? 'primary.light' : undefined,
                                opacity: formValues.groupBehavior === 'reference' ? 1 : 0.7,
                              }}
                              onClick={() => handleFormChange('groupBehavior', 'reference')}
                            >
                              <Typography variant="subtitle2" color={formValues.groupBehavior === 'reference' ? 'primary' : 'text.primary'}>
                                Reference First
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                First trait with a selection will be used as reference for others
                              </Typography>
                            </Paper>
                          </Box>
                        </FormControl>

                        <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                          Select traits to group together:
                        </Typography>

                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<AddIcon />}
                          onClick={() => {
                            // Add a new trait group
                            const newGroupTraits = [...groupTraits, { layerId: '', traitIds: [] }];
                            setGroupTraits(newGroupTraits);
                          }}
                          sx={{ mb: 2 }}
                        >
                          Add Trait Group
                        </Button>

                        {groupTraits.length > 0 ? (
                          groupTraits.map((group, groupIndex) => (
                            <Paper
                              key={`trait-group-${groupIndex}`}
                              elevation={1}
                              sx={{ p: 1.5, mb: 2, borderRadius: 1 }}
                            >
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="subtitle2">
                                  Trait Group #{groupIndex + 1}
                                </Typography>
                                <IconButton
                                  color="error"
                                  size="small"
                                  onClick={() => {
                                    // Remove this trait group
                                    const newGroupTraits = [...groupTraits];
                                    newGroupTraits.splice(groupIndex, 1);
                                    setGroupTraits(newGroupTraits);
                                  }}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Box>

                              <Grid container spacing={2}>
                                <Grid item xs={12}>
                                  <StyledFormControl
                                    label="Layer"
                                    variant="dialog"
                                    size="small"
                                  >
                                    <Select
                                      value={group.layerId || ''}
                                      onChange={(e) => {
                                        // Update layer ID for this group
                                        const newGroupTraits = [...groupTraits];
                                        newGroupTraits[groupIndex].layerId = e.target.value as string;
                                        newGroupTraits[groupIndex].traitIds = []; // Reset traits when layer changes
                                        setGroupTraits(newGroupTraits);
                                      }}
                                    >
                                      <MenuItem value=""><em>Select a Layer</em></MenuItem>
                                      {layers && layers.length > 0 ? (
                                        layers.map((layer) => (
                                          <MenuItem key={layer.id} value={layer.id}>
                                            {layer.name}
                                          </MenuItem>
                                        ))
                                      ) : (
                                        <MenuItem disabled>No layers available</MenuItem>
                                      )}
                                    </Select>
                                  </StyledFormControl>
                                </Grid>

                                <Grid item xs={12}>
                                  <StyledFormControl
                                    label="Traits"
                                    variant="dialog"
                                    size="small"
                                    disabled={!group.layerId}
                                  >
                                    <Select
                                      multiple
                                      value={group.traitIds || []}
                                      onChange={(e) => {
                                        // Update trait IDs for this group
                                        const newGroupTraits = [...groupTraits];
                                        newGroupTraits[groupIndex].traitIds = e.target.value as string[];
                                        setGroupTraits(newGroupTraits);
                                      }}
                                      renderValue={(selected) => {
                                        const selectedTraits = getTraitsForLayer(group.layerId)
                                          .filter(trait => (selected as string[]).includes(trait.id))
                                          .map(trait => trait.name);
                                        return selectedTraits.join(', ');
                                      }}
                                    >
                                      {getTraitsForLayer(group.layerId).map((trait) => (
                                        <MenuItem key={trait.id} value={trait.id}>
                                          <Checkbox checked={group.traitIds.indexOf(trait.id) > -1} />
                                          {trait.name}
                                        </MenuItem>
                                      ))}
                                    </Select>
                                  </StyledFormControl>
                                </Grid>
                              </Grid>
                            </Paper>
                          ))
                        ) : (
                          <Typography color="text.secondary">
                            No trait groups defined. Click the + button to add a group.
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  )}
                </Box>
              ) : (
                // Information display when no form is shown
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                  <Typography variant="subtitle1" gutterBottom>
                    {activeTab === 'trait' ? 'Trait Rule Management' : 'Group Rule Management'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" align="center" sx={{ maxWidth: 500, mb: 2 }}>
                    {activeTab === 'trait'
                      ? 'Create rules to control which traits can appear together. Rules help ensure your generated NFTs make visual sense.'
                      : 'Group layers that should be treated as a single layer for randomization. This helps with traits across different layers that should be considered as a single unit.'}
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={activeTab === 'trait' ? handleAddRule : handleAddGroupRule}
                  >
                    {activeTab === 'trait' ? 'Create a New Trait Rule' : 'Create a New Group Rule'}
                  </Button>
                </Box>
              )}
            </Box>
            {/* Fixed action buttons at the bottom - fixed height */}
            {showForm && (
              <Box
                sx={{
                  p: 1.5,
                  minHeight: '54px',
                  height: '54px',
                  borderTop: `1px solid ${theme.palette.divider}`,
                  backgroundColor: theme.palette.background.paper,
                  display: 'flex',
                  justifyContent: 'flex-end',
                  alignItems: 'center'
                }}
              >
                <Button
                  variant="outlined"
                  onClick={handleCancel}
                  sx={{ mr: 1.5 }}
                  size="small"
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleFormSubmit}
                  disabled={!formValues.name.trim() || (activeTab === 'layer-group' && groupLayers.length < 2)}
                  startIcon={<SaveIcon />}
                  size="small"
                >
                  {editingRuleId ? 'Update Rule' : 'Save Rule'}
                </Button>
              </Box>
            )}
          </Box>
        </Box>
      </DialogContent>
      {!showForm && (
        <DialogActions sx={{ borderTop: `1px solid ${theme.palette.divider}`, p: 1.5 }}>
          <Button onClick={onClose} variant="outlined" size="small">
            Close
          </Button>
        </DialogActions>
      )}
    </Dialog>
  );
};

export default RulesDialog;
