import React, { useMemo } from 'react';
import {
  Box,
  Typography,
  Button,
  Stack,
  Paper
} from '@mui/material';
import {
  Shuffle as ShuffleIcon
} from '@mui/icons-material';
import { useAppStore } from '@/application/stores/appStore';

/**
 * Preview Composition Component - Stacks trait images on top of each other
 */
const PreviewComposition: React.FC<{
  selectedTraits: Record<string, string>;
  layers: any[];
}> = ({ selectedTraits, layers }) => {
  const sortedLayers = useMemo(() => {
    return [...layers].sort((a, b) => a.order - b.order);
  }, [layers]);

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      {sortedLayers.map((layer) => {
        const selectedTraitId = selectedTraits[layer.id];

        // Find trait in trait groups
        let trait = null;
        for (const group of layer.traitGroups) {
          trait = group.traits.find((t: any) => t.id === selectedTraitId);
          if (trait) break;
        }

        if (!trait || !trait.imageUrl) return null;

        return (
          <Box
            key={layer.id}
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <img
              src={trait.imageUrl}
              alt={trait.name}
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain',
                zIndex: layer.order || 0
              }}
              onError={(e) => {
                console.log('Preview image load error for trait:', trait.name, 'URL:', trait.imageUrl);
                // Try to reload from persistent storage
                if (trait.imageUrl && trait.imageUrl.startsWith('blob:')) {
                  // Blob URL might be invalid, try to regenerate
                  import('@/services/memory/image-persistence.service').then(({ getTraitImage }) => {
                    getTraitImage(layer.name || 'unknown', trait.name).then(newUrl => {
                      if (newUrl && e.target) {
                        (e.target as HTMLImageElement).src = newUrl;
                      }
                    });
                  });
                }
              }}
            />
          </Box>
        );
      })}
    </Box>
  );
};

/**
 * Basit Preview Panel - Sadece temel randomization özelliği
 */
const PreviewPanel = () => {
  const { currentProject } = useAppStore();
  const layers = currentProject?.layers || [];

  // Randomize function - her layer'dan rastgele bir trait seç
  const handleRandomizeNFT = () => {
    if (!layers || layers.length === 0) {
      console.error('No layers available. Please import assets first.');
      return;
    }

    try {
      // Basit randomization - her layer'dan rastgele bir trait seç
      const selectedTraits: Record<string, string> = {};

      layers.forEach(layer => {
        // Get all traits from all trait groups
        const allTraits = layer.traitGroups.flatMap(group => group.traits);
        if (allTraits && allTraits.length > 0) {
          const randomIndex = Math.floor(Math.random() * allTraits.length);
          selectedTraits[layer.id] = allTraits[randomIndex].id;
        }
      });

      console.log('Randomized NFT with traits:', Object.values(selectedTraits).map(traitId => {
        // Find trait name for logging
        for (const layer of layers) {
          for (const group of layer.traitGroups) {
            const trait = group.traits.find(t => t.id === traitId);
            if (trait) return trait.name;
          }
        }
        return traitId;
      }));

      // TODO: Update selected traits in store
      // For now just log the selection
    } catch (error) {
      console.error('Error randomizing NFT:', error);
    }
  };

  // Get selected traits from layers (for now, just get first trait from each layer for demo)
  const selectedTraits = useMemo(() => {
    const traits: Record<string, string> = {};
    layers.forEach(layer => {
      if (layer.traitGroups.length > 0 && layer.traitGroups[0].traits.length > 0) {
        traits[layer.id] = layer.traitGroups[0].traits[0].id;
      }
    });
    return traits;
  }, [layers]);

  // Basit trait listesi
  const selectedTraitsList = useMemo(() => {
    if (!layers || !selectedTraits) return [];

    return layers.map(layer => {
      const traitId = selectedTraits[layer.id];
      // Find trait in trait groups
      let trait = null;
      for (const group of layer.traitGroups) {
        trait = group.traits.find(t => t.id === traitId);
        if (trait) break;
      }

      return {
        layerName: layer.name,
        traitName: trait?.name || 'None',
        rarity: trait?.rarity || 0
      };
    }).filter(item => item.traitName !== 'None');
  }, [layers, selectedTraits]);

  return (
    <Paper sx={{ p: 2, height: '100%' }}>
      <Stack spacing={2} sx={{ height: '100%' }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">NFT Preview</Typography>
          <Button
            variant="contained"
            startIcon={<ShuffleIcon />}
            onClick={handleRandomizeNFT}
            sx={{ width: '100%', maxWidth: 200 }}
          >
            Randomize
          </Button>
        </Box>

        {/* Preview Area */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: '2px dashed #ccc',
            borderRadius: 2,
            minHeight: 300,
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {selectedTraitsList.length > 0 ? (
            <PreviewComposition selectedTraits={selectedTraits} layers={layers} />
          ) : (
            <Typography variant="body1" color="text.secondary">
              Click "Randomize" to generate an NFT preview
            </Typography>
          )}
        </Box>

        {/* Selected Traits List */}
        {selectedTraitsList.length > 0 && (
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Selected Traits:
            </Typography>
            <Stack spacing={1}>
              {selectedTraitsList.map((trait, index) => (
                <Box
                  key={index}
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    p: 1,
                    bgcolor: 'background.default',
                    borderRadius: 1
                  }}
                >
                  <Typography variant="body2" color="text.secondary">
                    {trait.layerName}:
                  </Typography>
                  <Typography variant="body2">
                    {trait.traitName} ({trait.rarity.toFixed(1)}%)
                  </Typography>
                </Box>
              ))}
            </Stack>
          </Box>
        )}
      </Stack>
    </Paper>
  );
};

export default PreviewPanel;