/**
 * Hook for initializing storage on app startup
 */

import { useEffect } from 'react'
import { useAppStore } from '../application/stores/appStore'

export const useStorageInitialization = () => {
  const initializeStorage = useAppStore(state => state.initializeStorage)
  const isLoading = useAppStore(state => state.isLoading)

  useEffect(() => {
    // Initialize storage when the app starts
    initializeStorage()
  }, [initializeStorage])

  return {
    isInitializing: isLoading
  }
}
