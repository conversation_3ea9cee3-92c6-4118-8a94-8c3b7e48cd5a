{"version": "3.1.4", "results": [[":src/tests/content-analysis/ContentAnalysisIntegration.test.ts", {"duration": 38.2822000000001, "failed": true}], [":src/domain/entities/__tests__/Project.test.ts", {"duration": 13.589199999999892, "failed": false}], [":src/test/persistence.test.ts", {"duration": 7.674199999999928, "failed": false}], [":src/infrastructure/services/__tests__/LayerImport.integration.test.ts", {"duration": 14.432299999999941, "failed": true}], [":src/test/components.integration.test.tsx", {"duration": 215.53999999999996, "failed": false}], [":src/infrastructure/services/__tests__/LayerImportService.test.ts", {"duration": 8.110600000000204, "failed": false}], [":src/test/e2e.scenarios.test.tsx", {"duration": 9.895800000000008, "failed": true}]]}