/**
 * Simple Name-Based Relationship Analyzer
 * Analyzes folder structure and file names to detect trait relationships
 */

export interface TraitFile {
  layerName: string;
  fileName: string;
  filePath: string;
  extractedKeywords: string[];
}

export interface RelationshipGroup {
  keyword: string;
  layers: {
    layerName: string;
    files: TraitFile[];
    count: number;
  }[];
  totalFiles: number;
}

export interface RuleSuggestion {
  triggerLayer: string;
  triggerValue: string;
  targetLayer: string;
  targetValues: string[];
  confidence: number;
  description: string;
}

export class SimpleRelationshipAnalyzer {

  /**
   * ENHANCED: Extract keywords from full file path with hierarchical analysis
   * Handles patterns like: "Project/Eye Color/Double Color/Angry/Blue-Green.png"
   * Supports cross-layer trait matching and variant recognition
   */
  private extractKeywordsFromPath(filePath: string): string[] {
    const keywords: string[] = [];
    const pathParts = filePath.split('/');

    // ENHANCED: Hierarchical path analysis
    const pathAnalysis = this.analyzeHierarchicalStructure(pathParts);

    // Add hierarchical context keywords
    keywords.push(...pathAnalysis.layerKeywords);
    keywords.push(...pathAnalysis.traitGroupKeywords);
    keywords.push(...pathAnalysis.variantKeywords);
    keywords.push(...pathAnalysis.contextKeywords);

    // Extract keywords from folder names (skip project and layer names)
    const folderParts = pathParts.slice(pathAnalysis.startIndex, -1); // Middle folders
    const fileName = pathParts[pathParts.length - 1]; // Last part (filename)

    // Extract keywords from folder names with enhanced pattern recognition
    folderParts.forEach((folderName, index) => {
      const folderKeywords = this.extractKeywords(folderName);
      keywords.push(...folderKeywords);

      // ENHANCED: Add hierarchical level context
      keywords.push(`level_${index + 1}_${folderName.toLowerCase()}`);
    });

    // Extract keywords from filename with variant detection
    const fileKeywords = this.extractKeywordsWithVariants(fileName);
    keywords.push(...fileKeywords);

    // Remove duplicates and return
    return [...new Set(keywords)];
  }

  /**
   * ENHANCED: Analyze hierarchical folder structure for better context understanding
   */
  private analyzeHierarchicalStructure(pathParts: string[]): {
    layerKeywords: string[];
    traitGroupKeywords: string[];
    variantKeywords: string[];
    contextKeywords: string[];
    startIndex: number;
  } {
    const layerKeywords: string[] = [];
    const traitGroupKeywords: string[] = [];
    const variantKeywords: string[] = [];
    const contextKeywords: string[] = [];

    // Determine structure: Project/Layer/TraitGroup1/TraitGroup2/Variant/File.png
    let startIndex = 1; // Skip layer name by default

    if (pathParts.length >= 3) {
      startIndex = 2; // Skip project + layer name

      // First level = Layer name (index 1)
      const layerName = pathParts[1].toLowerCase();
      layerKeywords.push(layerName);
      layerKeywords.push(`layer_${layerName}`);

      // Detect layer type for better context
      const layerType = this.getLayerType(layerName);
      if (layerType !== 'other') {
        contextKeywords.push(`type_${layerType}`);
      }
    }

    // Analyze middle levels (trait groups and variants)
    for (let i = startIndex; i < pathParts.length - 1; i++) {
      const folderName = pathParts[i].toLowerCase();
      const level = i - startIndex + 1;

      // Level 1-2: Trait groups (Double Color, Single Color, etc.)
      if (level <= 2) {
        traitGroupKeywords.push(folderName);
        traitGroupKeywords.push(`group_${folderName}`);

        // Detect style patterns
        if (folderName.includes('color')) {
          traitGroupKeywords.push('color_variant');
          contextKeywords.push('has_color_variants');
        }
        if (folderName.includes('double') || folderName.includes('single')) {
          traitGroupKeywords.push('style_variant');
          contextKeywords.push('has_style_variants');
        }
      }
      // Level 3+: Specific variants (Angry, Happy, etc.)
      else {
        variantKeywords.push(folderName);
        variantKeywords.push(`variant_${folderName}`);

        // Detect emotion variants
        if (this.isEmotionKeyword(folderName)) {
          contextKeywords.push('has_emotion_variants');
          contextKeywords.push(`emotion_${folderName}`);
        }
      }
    }

    return { layerKeywords, traitGroupKeywords, variantKeywords, contextKeywords, startIndex };
  }

  /**
   * ENHANCED: Extract keywords with variant pattern recognition
   */
  private extractKeywordsWithVariants(fileName: string): string[] {
    const keywords = this.extractKeywords(fileName);
    const enhancedKeywords: string[] = [...keywords];

    // Detect variant patterns (Face1, Face2, Hair3, etc.)
    keywords.forEach(keyword => {
      const variantMatch = keyword.match(/^([a-zA-Z]+)(\d+)$/);
      if (variantMatch) {
        const [, baseName, variantNumber] = variantMatch;
        enhancedKeywords.push(`${baseName.toLowerCase()}_variant`);
        enhancedKeywords.push(`variant_${variantNumber}`);
        enhancedKeywords.push(`base_${baseName.toLowerCase()}`);
      }

      // Detect color patterns (Blue-Green, Red-Blue, etc.)
      if (keyword.includes('-') && this.isColorKeyword(keyword.split('-')[0])) {
        enhancedKeywords.push('color_combination');
        keyword.split('-').forEach(colorPart => {
          if (this.isColorKeyword(colorPart.toLowerCase())) {
            enhancedKeywords.push(`color_${colorPart.toLowerCase()}`);
          }
        });
      }
    });

    return enhancedKeywords;
  }

  /**
   * Extract keywords from filename or folder name
   * Handles patterns like: "Mathilda Blue (Angry).png", "Blue - Green/Happy.png"
   */
  private extractKeywords(fileName: string): string[] {
    const keywords: string[] = [];

    // Remove file extension
    const nameWithoutExt = fileName.replace(/\.(png|jpg|jpeg|gif|svg|webp|tiff|tif)$/i, '');

    // Extract content from parentheses: (Angry), (The Rest)
    const parenthesesMatches = nameWithoutExt.match(/\(([^)]+)\)/g);
    if (parenthesesMatches) {
      parenthesesMatches.forEach(match => {
        const content = match.replace(/[()]/g, '').trim();
        keywords.push(content);
      });
    }

    // Extract words separated by spaces, dashes, underscores
    const words = nameWithoutExt
      .replace(/\([^)]*\)/g, '') // Remove parentheses content
      .split(/[\s\-_]+/)
      .map(word => word.trim())
      .filter(word => word.length > 0);

    keywords.push(...words);

    // Clean and normalize keywords
    return keywords
      .map(keyword => keyword.trim())
      .filter(keyword => keyword.length > 0)
      .map(keyword => this.normalizeKeyword(keyword));
  }

  /**
   * Normalize keyword for better matching
   */
  private normalizeKeyword(keyword: string): string {
    // Convert to lowercase and handle special cases
    const normalized = keyword.toLowerCase().trim();

    // Handle special cases
    const specialCases: { [key: string]: string } = {
      'laaame!': 'lame',
      'lame!': 'lame',
      'the rest': 'rest',
      'surprised & heartbreaking': 'surprised',
      'raised eyebrows': 'raised',
      'rolling eyes': 'rolling',
      'tongue out': 'tongue'
    };

    return specialCases[normalized] || normalized;
  }



  /**
   * Analyze real FileList from import
   */
  public analyzeImportedFiles(files: FileList): {
    traitFiles: TraitFile[];
    relationships: RelationshipGroup[];
    suggestions: RuleSuggestion[];
  } {
    // Extract trait files from FileList
    const traitFiles = this.extractTraitFilesFromFileList(files);

    // Group by keywords
    const relationships = this.groupByKeywords(traitFiles);

    // Generate rule suggestions
    const suggestions = this.generateRuleSuggestions(relationships);

    console.log('🔍 Starting Real File Analysis...');
    console.log(`📁 Analyzing ${files.length} files`);
    console.log(`📁 Found ${traitFiles.length} trait files`);
    console.log(`🔗 Found ${relationships.length} relationship groups`);
    console.log(`💡 Generated ${suggestions.length} rule suggestions`);

    return {
      traitFiles,
      relationships,
      suggestions
    };
  }

  /**
   * Extract trait files from real FileList
   */
  private extractTraitFilesFromFileList(files: FileList): TraitFile[] {
    const traitFiles: TraitFile[] = [];
    const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.tiff', '.tif'];

    Array.from(files).forEach(file => {
      // Check if it's an image file
      const fileName = file.name.toLowerCase();
      const isImage = imageExtensions.some(ext => fileName.endsWith(ext));

      if (isImage && file.webkitRelativePath) {
        // Parse the path: "ProjectName/LayerName/SubFolder/FileName.png"
        const pathParts = file.webkitRelativePath.split('/');

        if (pathParts.length >= 2) {
          // Determine layer name - skip project folder if present
          let layerName: string;
          let startIndex = 0;

          if (pathParts.length >= 3) {
            // Has project folder: "project/Faces/Angry.png" -> layer = "Faces"
            layerName = pathParts[1];
            startIndex = 1;
          } else {
            // Direct layer: "Faces/Angry.png" -> layer = "Faces"
            layerName = pathParts[0];
            startIndex = 0;
          }

          const fileName = pathParts[pathParts.length - 1]; // Last part is file name

          // Extract keywords from both filename AND folder structure
          const allKeywords = this.extractKeywordsFromPath(file.webkitRelativePath);

          traitFiles.push({
            layerName,
            fileName,
            filePath: file.webkitRelativePath,
            extractedKeywords: allKeywords
          });
        }
      }
    });

    return traitFiles;
  }



  /**
   * Group trait files by common keywords
   */
  private groupByKeywords(traitFiles: TraitFile[]): RelationshipGroup[] {
    const keywordMap = new Map<string, Map<string, TraitFile[]>>();

    // Group files by keyword and layer
    traitFiles.forEach(file => {
      file.extractedKeywords.forEach(keyword => {
        if (!keywordMap.has(keyword)) {
          keywordMap.set(keyword, new Map());
        }

        const layerMap = keywordMap.get(keyword)!;
        if (!layerMap.has(file.layerName)) {
          layerMap.set(file.layerName, []);
        }

        layerMap.get(file.layerName)!.push(file);
      });
    });

    // Convert to RelationshipGroup format
    const relationships: RelationshipGroup[] = [];

    keywordMap.forEach((layerMap, keyword) => {
      const layers: RelationshipGroup['layers'] = [];
      let totalFiles = 0;

      layerMap.forEach((files, layerName) => {
        layers.push({
          layerName,
          files,
          count: files.length
        });
        totalFiles += files.length;
      });

      // Only include keywords that appear in multiple layers or multiple times
      if (layers.length > 1 || totalFiles > 1) {
        relationships.push({
          keyword,
          layers,
          totalFiles
        });
      }
    });

    // Sort by relevance (more layers = more important)
    return relationships.sort((a, b) => {
      if (a.layers.length !== b.layers.length) {
        return b.layers.length - a.layers.length;
      }
      return b.totalFiles - a.totalFiles;
    });
  }

  /**
   * ENHANCED: Generate SMART rule suggestions with advanced pattern recognition
   */
  private generateRuleSuggestions(relationships: RelationshipGroup[]): RuleSuggestion[] {
    const suggestions: RuleSuggestion[] = [];

    relationships.forEach(relationship => {
      const { keyword, layers } = relationship;

      // Skip single-layer relationships
      if (layers.length < 2) return;

      // ENHANCED: Advanced pattern analysis
      const smartSuggestions = this.generateAdvancedSmartSuggestions(keyword, layers);
      suggestions.push(...smartSuggestions);
    });

    // ENHANCED: Cross-layer relationship detection
    const crossLayerSuggestions = this.generateCrossLayerRelationships(relationships);
    suggestions.push(...crossLayerSuggestions);

    return suggestions
      .filter(s => s.confidence > 0.3) // Only keep meaningful suggestions
      .sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * ENHANCED: Generate cross-layer relationship suggestions
   * Detects patterns like Face/Eye color matching, Hair/Eyewear coordination
   */
  private generateCrossLayerRelationships(relationships: RelationshipGroup[]): RuleSuggestion[] {
    const suggestions: RuleSuggestion[] = [];

    // Group relationships by layer types for better analysis
    const layerTypeGroups = this.groupRelationshipsByLayerType(relationships);

    // Face/Eye color coordination
    const faceEyeRules = this.generateFaceEyeCoordination(layerTypeGroups);
    suggestions.push(...faceEyeRules);

    // Hair/Accessory coordination
    const hairAccessoryRules = this.generateHairAccessoryCoordination(layerTypeGroups);
    suggestions.push(...hairAccessoryRules);

    // Style consistency across layers
    const styleConsistencyRules = this.generateStyleConsistencyAcrossLayers(layerTypeGroups);
    suggestions.push(...styleConsistencyRules);

    return suggestions;
  }

  /**
   * Group relationships by layer types for better analysis
   */
  private groupRelationshipsByLayerType(relationships: RelationshipGroup[]): Map<string, RelationshipGroup[]> {
    const layerTypeGroups = new Map<string, RelationshipGroup[]>();

    relationships.forEach(relationship => {
      relationship.layers.forEach(layer => {
        const layerType = this.getLayerType(layer.layerName);
        if (!layerTypeGroups.has(layerType)) {
          layerTypeGroups.set(layerType, []);
        }
        layerTypeGroups.get(layerType)!.push(relationship);
      });
    });

    return layerTypeGroups;
  }

  /**
   * ENHANCED: Generate Face/Eye color coordination rules
   */
  private generateFaceEyeCoordination(layerTypeGroups: Map<string, RelationshipGroup[]>): RuleSuggestion[] {
    const suggestions: RuleSuggestion[] = [];
    const faceRelationships = layerTypeGroups.get('face') || [];
    const eyeRelationships = layerTypeGroups.get('eyes') || [];

    // Find common emotion/expression keywords between face and eyes
    faceRelationships.forEach(faceRel => {
      eyeRelationships.forEach(eyeRel => {
        if (faceRel.keyword === eyeRel.keyword && this.isEmotionKeyword(faceRel.keyword)) {
          const faceLayers = faceRel.layers.filter(l => this.getLayerType(l.layerName) === 'face');
          const eyeLayers = eyeRel.layers.filter(l => this.getLayerType(l.layerName) === 'eyes');

          faceLayers.forEach(faceLayer => {
            eyeLayers.forEach(eyeLayer => {
              suggestions.push({
                triggerLayer: faceLayer.layerName,
                triggerValue: `*${faceRel.keyword}*`,
                targetLayer: eyeLayer.layerName,
                targetValues: eyeLayer.files
                  .filter(f => f.extractedKeywords.includes(faceRel.keyword))
                  .map(f => this.getFileNameWithoutExtension(f.fileName)),
                confidence: 0.85,
                description: `Face/Eye coordination: ${faceRel.keyword} face expression should match ${eyeRel.keyword} eye expression`
              });
            });
          });
        }
      });
    });

    return suggestions;
  }

  /**
   * ENHANCED: Generate Hair/Accessory coordination rules
   */
  private generateHairAccessoryCoordination(layerTypeGroups: Map<string, RelationshipGroup[]>): RuleSuggestion[] {
    const suggestions: RuleSuggestion[] = [];
    const hairRelationships = layerTypeGroups.get('hair') || [];
    const accessoryRelationships = layerTypeGroups.get('accessory') || [];

    // Find style/color coordination between hair and accessories
    hairRelationships.forEach(hairRel => {
      accessoryRelationships.forEach(accRel => {
        if (hairRel.keyword === accRel.keyword &&
            (this.isColorKeyword(hairRel.keyword) || this.isStyleKeyword(hairRel.keyword))) {

          const hairLayers = hairRel.layers.filter(l => this.getLayerType(l.layerName) === 'hair');
          const accLayers = accRel.layers.filter(l => this.getLayerType(l.layerName) === 'accessory');

          hairLayers.forEach(hairLayer => {
            accLayers.forEach(accLayer => {
              suggestions.push({
                triggerLayer: hairLayer.layerName,
                triggerValue: `*${hairRel.keyword}*`,
                targetLayer: accLayer.layerName,
                targetValues: accLayer.files
                  .filter(f => f.extractedKeywords.includes(hairRel.keyword))
                  .map(f => this.getFileNameWithoutExtension(f.fileName)),
                confidence: 0.75,
                description: `Hair/Accessory coordination: ${hairRel.keyword} hair should coordinate with ${accRel.keyword} accessories`
              });
            });
          });
        }
      });
    });

    return suggestions;
  }

  /**
   * ENHANCED: Generate style consistency across multiple layers
   */
  private generateStyleConsistencyAcrossLayers(layerTypeGroups: Map<string, RelationshipGroup[]>): RuleSuggestion[] {
    const suggestions: RuleSuggestion[] = [];

    // Find style keywords that appear across multiple layer types
    const styleKeywords = new Set<string>();
    layerTypeGroups.forEach(relationships => {
      relationships.forEach(rel => {
        if (this.isStyleKeyword(rel.keyword) || this.isThemeKeyword(rel.keyword)) {
          styleKeywords.add(rel.keyword);
        }
      });
    });

    // Generate cross-layer style consistency rules
    styleKeywords.forEach(styleKeyword => {
      const layersWithStyle: { layerType: string; layers: any[] }[] = [];

      layerTypeGroups.forEach((relationships, layerType) => {
        const matchingLayers = relationships
          .filter(rel => rel.keyword === styleKeyword)
          .flatMap(rel => rel.layers);

        if (matchingLayers.length > 0) {
          layersWithStyle.push({ layerType, layers: matchingLayers });
        }
      });

      // Create rules between different layer types
      if (layersWithStyle.length >= 2) {
        layersWithStyle.forEach(triggerGroup => {
          layersWithStyle.forEach(targetGroup => {
            if (triggerGroup.layerType !== targetGroup.layerType) {
              triggerGroup.layers.forEach(triggerLayer => {
                targetGroup.layers.forEach(targetLayer => {
                  suggestions.push({
                    triggerLayer: triggerLayer.layerName,
                    triggerValue: `*${styleKeyword}*`,
                    targetLayer: targetLayer.layerName,
                    targetValues: targetLayer.files
                      .filter((f: any) => f.extractedKeywords.includes(styleKeyword))
                      .map((f: any) => this.getFileNameWithoutExtension(f.fileName)),
                    confidence: 0.8,
                    description: `Style consistency: ${styleKeyword} style should be consistent across ${triggerGroup.layerType} and ${targetGroup.layerType}`
                  });
                });
              });
            }
          });
        });
      }
    });

    return suggestions;
  }

  /**
   * ENHANCED: Generate advanced smart suggestions with improved pattern recognition
   */
  private generateAdvancedSmartSuggestions(keyword: string, layers: RelationshipGroup['layers']): RuleSuggestion[] {
    const suggestions: RuleSuggestion[] = [];
    const keywordLower = keyword.toLowerCase();

    // ENHANCED: Context-aware keyword analysis
    const keywordContext = this.analyzeKeywordContext(keyword, layers);

    // EMOTION-BASED RULES (Face expressions → other traits)
    if (this.isEmotionKeyword(keywordLower)) {
      const emotionSuggestions = this.generateEnhancedEmotionBasedRules(keyword, layers, keywordContext);
      suggestions.push(...emotionSuggestions);
    }

    // COLOR HARMONY RULES (Color coordination across layers)
    else if (this.isColorKeyword(keywordLower)) {
      const colorSuggestions = this.generateEnhancedColorHarmonyRules(keyword, layers, keywordContext);
      suggestions.push(...colorSuggestions);
    }

    // STYLE CONSISTENCY RULES (Style matching)
    else if (this.isStyleKeyword(keywordLower)) {
      const styleSuggestions = this.generateStyleConsistencyRules(keyword, layers);
      suggestions.push(...styleSuggestions);
    }

    // THEMATIC RULES (Theme-based matching)
    else if (this.isThemeKeyword(keywordLower)) {
      const themeSuggestions = this.generateThematicRules(keyword, layers);
      suggestions.push(...themeSuggestions);
    }

    // ENHANCED: Variant-based rules (Face1, Face2, etc.)
    else if (keywordContext.isVariant) {
      const variantSuggestions = this.generateVariantBasedRules(keyword, layers, keywordContext);
      suggestions.push(...variantSuggestions);
    }

    return suggestions;
  }

  /**
   * ENHANCED: Analyze keyword context for better rule generation
   */
  private analyzeKeywordContext(keyword: string, layers: RelationshipGroup['layers']): {
    isVariant: boolean;
    variantBase?: string;
    variantNumber?: string;
    layerTypes: string[];
    hierarchicalLevel: number;
    confidence: number;
  } {
    const layerTypes = layers.map(layer => this.getLayerType(layer.layerName));

    // Check if keyword is a variant (Face1, Hair2, etc.)
    const variantMatch = keyword.match(/^([a-zA-Z]+)(\d+)$/);
    const isVariant = !!variantMatch;

    // Determine hierarchical level based on layer types
    let hierarchicalLevel = 1;
    if (layerTypes.includes('face') || layerTypes.includes('eyes')) {
      hierarchicalLevel = 3; // High importance
    } else if (layerTypes.includes('hair') || layerTypes.includes('clothing')) {
      hierarchicalLevel = 2; // Medium importance
    }

    // Calculate confidence based on context
    let confidence = 0.5;
    if (isVariant) confidence += 0.2;
    if (layerTypes.length > 1) confidence += 0.1;
    if (hierarchicalLevel > 1) confidence += 0.1;

    return {
      isVariant,
      variantBase: variantMatch?.[1],
      variantNumber: variantMatch?.[2],
      layerTypes,
      hierarchicalLevel,
      confidence: Math.min(confidence, 1.0)
    };
  }

  /**
   * Check if keyword represents an emotion
   */
  private isEmotionKeyword(keyword: string): boolean {
    const emotions = [
      'angry', 'happy', 'sad', 'surprised', 'neutral', 'smile', 'frown',
      'laugh', 'cry', 'wink', 'kiss', 'shocked', 'confused', 'sleepy',
      'excited', 'bored', 'worried', 'calm', 'fierce', 'gentle'
    ];
    return emotions.includes(keyword);
  }

  /**
   * Check if keyword represents a color
   */
  private isColorKeyword(keyword: string): boolean {
    const colors = [
      'red', 'blue', 'green', 'yellow', 'orange', 'purple', 'pink', 'brown',
      'black', 'white', 'gray', 'grey', 'gold', 'silver', 'cyan', 'magenta',
      'lime', 'navy', 'maroon', 'olive', 'teal', 'aqua', 'fuchsia'
    ];
    return colors.includes(keyword);
  }

  /**
   * Check if keyword represents a style
   */
  private isStyleKeyword(keyword: string): boolean {
    const styles = [
      'vintage', 'modern', 'classic', 'retro', 'futuristic', 'minimalist',
      'ornate', 'simple', 'complex', 'elegant', 'casual', 'formal',
      'punk', 'gothic', 'steampunk', 'cyberpunk', 'fantasy', 'sci-fi'
    ];
    return styles.includes(keyword);
  }

  /**
   * Check if keyword represents a theme
   */
  private isThemeKeyword(keyword: string): boolean {
    const themes = [
      'pirate', 'ninja', 'robot', 'alien', 'zombie', 'vampire', 'angel',
      'demon', 'wizard', 'warrior', 'princess', 'king', 'queen', 'knight',
      'space', 'ocean', 'forest', 'desert', 'mountain', 'city', 'village'
    ];
    return themes.includes(keyword);
  }

  /**
   * ENHANCED: Generate variant-based rules for Face1, Face2, etc.
   */
  private generateVariantBasedRules(keyword: string, layers: RelationshipGroup['layers'], context: any): RuleSuggestion[] {
    const suggestions: RuleSuggestion[] = [];

    if (!context.isVariant || !context.variantBase) return suggestions;

    // Find layers that should coordinate with this variant
    const triggerLayers = layers.filter(layer =>
      this.getLayerType(layer.layerName) === 'face' ||
      this.getLayerType(layer.layerName) === 'eyes'
    );

    const targetLayers = layers.filter(layer =>
      this.getLayerType(layer.layerName) !== 'face' &&
      this.getLayerType(layer.layerName) !== 'eyes'
    );

    triggerLayers.forEach(triggerLayer => {
      targetLayers.forEach(targetLayer => {
        const targetTraits = targetLayer.files
          .filter(file => file.extractedKeywords.some(kw =>
            kw.includes(context.variantBase) || kw.includes(context.variantNumber)
          ))
          .map(file => this.getFileNameWithoutExtension(file.fileName));

        if (targetTraits.length > 0) {
          suggestions.push({
            triggerLayer: triggerLayer.layerName,
            triggerValue: `*${keyword}*`,
            targetLayer: targetLayer.layerName,
            targetValues: targetTraits,
            confidence: context.confidence,
            description: `Variant coordination: ${keyword} should coordinate with matching ${targetLayer.layerName} variants`
          });
        }
      });
    });

    return suggestions;
  }

  /**
   * ENHANCED: Generate emotion-based rules with improved context awareness
   */
  private generateEnhancedEmotionBasedRules(emotion: string, layers: RelationshipGroup['layers'], context: any): RuleSuggestion[] {
    const suggestions: RuleSuggestion[] = [];

    // Find face/expression layer as trigger with enhanced detection
    const faceLayer = layers.find(layer => {
      const layerType = this.getLayerType(layer.layerName);
      return layerType === 'face' || layerType === 'eyes' ||
             layer.layerName.toLowerCase().includes('expression') ||
             layer.layerName.toLowerCase().includes('mouth');
    });

    if (!faceLayer) return suggestions;

    // Generate rules for other layers that should match the emotion
    layers.forEach(targetLayer => {
      if (targetLayer.layerName === faceLayer.layerName) return;

      const layerType = this.getLayerType(targetLayer.layerName);
      const shouldMatch = this.shouldEmotionMatch(emotion, layerType);

      if (shouldMatch.match) {
        const targetTraits = targetLayer.files
          .filter(file => file.extractedKeywords.includes(emotion))
          .map(file => this.getFileNameWithoutExtension(file.fileName));

        if (targetTraits.length > 0) {
          suggestions.push({
            triggerLayer: faceLayer.layerName,
            triggerValue: `*${emotion}*`,
            targetLayer: targetLayer.layerName,
            targetValues: targetTraits,
            confidence: shouldMatch.confidence * context.confidence,
            description: `${shouldMatch.reason}: When face shows "${emotion}", ${targetLayer.layerName} should match`
          });
        }
      }
    });

    return suggestions;
  }

  /**
   * ENHANCED: Generate color harmony rules with improved pattern recognition
   */
  private generateEnhancedColorHarmonyRules(color: string, layers: RelationshipGroup['layers'], context: any): RuleSuggestion[] {
    const suggestions: RuleSuggestion[] = [];

    // Enhanced color layer detection
    const colorLayers = layers.filter(layer => {
      const layerType = this.getLayerType(layer.layerName);
      return ['clothing', 'accessory', 'hair', 'eyes', 'background'].includes(layerType) ||
             layer.layerName.toLowerCase().includes('color');
    });

    if (colorLayers.length < 2) return suggestions;

    // Generate complementary color rules with enhanced logic
    colorLayers.forEach(triggerLayer => {
      colorLayers.forEach(targetLayer => {
        if (triggerLayer.layerName === targetLayer.layerName) return;

        const harmony = this.getEnhancedColorHarmony(color, triggerLayer.layerName, targetLayer.layerName);
        if (harmony.shouldMatch) {
          const targetTraits = targetLayer.files
            .filter(file => file.extractedKeywords.includes(color) ||
                           file.extractedKeywords.some(kw => kw.includes('color')))
            .map(file => this.getFileNameWithoutExtension(file.fileName));

          if (targetTraits.length > 0) {
            suggestions.push({
              triggerLayer: triggerLayer.layerName,
              triggerValue: `*${color}*`,
              targetLayer: targetLayer.layerName,
              targetValues: targetTraits,
              confidence: harmony.confidence * context.confidence,
              description: `Enhanced color harmony: ${color} ${triggerLayer.layerName} pairs well with ${color} ${targetLayer.layerName}`
            });
          }
        }
      });
    });

    return suggestions;
  }

  /**
   * ENHANCED: Get color harmony with improved logic
   */
  private getEnhancedColorHarmony(color: string, triggerLayerName: string, targetLayerName: string): { shouldMatch: boolean; confidence: number } {
    const triggerType = this.getLayerType(triggerLayerName);
    const targetType = this.getLayerType(targetLayerName);

    // Enhanced hair and eyes coordination
    if ((triggerType === 'hair' && targetType === 'eyes') || (triggerType === 'eyes' && targetType === 'hair')) {
      return { shouldMatch: true, confidence: 0.8 };
    }

    // Enhanced clothing and accessories coordination
    if ((triggerType === 'clothing' && targetType === 'accessory') || (triggerType === 'accessory' && targetType === 'clothing')) {
      return { shouldMatch: true, confidence: 0.85 };
    }

    // Face and eye color coordination
    if ((triggerType === 'face' && targetType === 'eyes') || (triggerType === 'eyes' && targetType === 'face')) {
      return { shouldMatch: true, confidence: 0.9 };
    }

    // Background rarely coordinates with character features
    if (triggerType === 'background' || targetType === 'background') {
      return { shouldMatch: false, confidence: 0 };
    }

    return { shouldMatch: false, confidence: 0 };
  }

  /**
   * Generate emotion-based rules (Face expressions → matching accessories/clothes)
   */
  private generateEmotionBasedRules(emotion: string, layers: RelationshipGroup['layers']): RuleSuggestion[] {
    const suggestions: RuleSuggestion[] = [];

    // Find face/expression layer as trigger
    const faceLayer = layers.find(layer =>
      layer.layerName.toLowerCase().includes('face') ||
      layer.layerName.toLowerCase().includes('expression') ||
      layer.layerName.toLowerCase().includes('mouth') ||
      layer.layerName.toLowerCase().includes('eye')
    );

    if (!faceLayer) return suggestions;

    // Generate rules for other layers that should match the emotion
    layers.forEach(targetLayer => {
      if (targetLayer.layerName === faceLayer.layerName) return;

      const layerType = this.getLayerType(targetLayer.layerName);
      const shouldMatch = this.shouldEmotionMatch(emotion, layerType);

      if (shouldMatch.match) {
        const targetTraits = targetLayer.files
          .filter(file => file.extractedKeywords.includes(emotion))
          .map(file => this.getFileNameWithoutExtension(file.fileName));

        if (targetTraits.length > 0) {
          suggestions.push({
            triggerLayer: faceLayer.layerName,
            triggerValue: `*${emotion}*`,
            targetLayer: targetLayer.layerName,
            targetValues: targetTraits,
            confidence: shouldMatch.confidence,
            description: `${shouldMatch.reason}: When face shows "${emotion}", ${targetLayer.layerName} should match`
          });
        }
      }
    });

    return suggestions;
  }

  /**
   * Generate color harmony rules
   */
  private generateColorHarmonyRules(color: string, layers: RelationshipGroup['layers']): RuleSuggestion[] {
    const suggestions: RuleSuggestion[] = [];

    // Color harmony only makes sense for certain layer combinations
    const colorLayers = layers.filter(layer => {
      const layerType = this.getLayerType(layer.layerName);
      return ['clothing', 'accessory', 'hair', 'eyes', 'background'].includes(layerType);
    });

    if (colorLayers.length < 2) return suggestions;

    // Generate complementary color rules
    colorLayers.forEach(triggerLayer => {
      colorLayers.forEach(targetLayer => {
        if (triggerLayer.layerName === targetLayer.layerName) return;

        const harmony = this.getColorHarmony(color, triggerLayer.layerName, targetLayer.layerName);
        if (harmony.shouldMatch) {
          const targetTraits = targetLayer.files
            .filter(file => file.extractedKeywords.includes(color))
            .map(file => this.getFileNameWithoutExtension(file.fileName));

          if (targetTraits.length > 0) {
            suggestions.push({
              triggerLayer: triggerLayer.layerName,
              triggerValue: `*${color}*`,
              targetLayer: targetLayer.layerName,
              targetValues: targetTraits,
              confidence: harmony.confidence,
              description: `Color harmony: ${color} ${triggerLayer.layerName} pairs well with ${color} ${targetLayer.layerName}`
            });
          }
        }
      });
    });

    return suggestions;
  }

  /**
   * Generate style consistency rules
   */
  private generateStyleConsistencyRules(style: string, layers: RelationshipGroup['layers']): RuleSuggestion[] {
    const suggestions: RuleSuggestion[] = [];

    // Style consistency applies to clothing, accessories, and background
    const styleLayers = layers.filter(layer => {
      const layerType = this.getLayerType(layer.layerName);
      return ['clothing', 'accessory', 'hat', 'background', 'decoration'].includes(layerType);
    });

    if (styleLayers.length < 2) return suggestions;

    // Generate style matching rules
    styleLayers.forEach(triggerLayer => {
      styleLayers.forEach(targetLayer => {
        if (triggerLayer.layerName === targetLayer.layerName) return;

        const targetTraits = targetLayer.files
          .filter(file => file.extractedKeywords.includes(style))
          .map(file => this.getFileNameWithoutExtension(file.fileName));

        if (targetTraits.length > 0) {
          suggestions.push({
            triggerLayer: triggerLayer.layerName,
            triggerValue: `*${style}*`,
            targetLayer: targetLayer.layerName,
            targetValues: targetTraits,
            confidence: 0.8,
            description: `Style consistency: ${style} ${triggerLayer.layerName} should match with ${style} ${targetLayer.layerName}`
          });
        }
      });
    });

    return suggestions;
  }

  /**
   * Generate thematic rules
   */
  private generateThematicRules(theme: string, layers: RelationshipGroup['layers']): RuleSuggestion[] {
    const suggestions: RuleSuggestion[] = [];

    // Themes apply to most layers
    layers.forEach(triggerLayer => {
      layers.forEach(targetLayer => {
        if (triggerLayer.layerName === targetLayer.layerName) return;

        const targetTraits = targetLayer.files
          .filter(file => file.extractedKeywords.includes(theme))
          .map(file => this.getFileNameWithoutExtension(file.fileName));

        if (targetTraits.length > 0) {
          suggestions.push({
            triggerLayer: triggerLayer.layerName,
            triggerValue: `*${theme}*`,
            targetLayer: targetLayer.layerName,
            targetValues: targetTraits,
            confidence: 0.9,
            description: `Thematic consistency: ${theme} theme should be consistent across ${triggerLayer.layerName} and ${targetLayer.layerName}`
          });
        }
      });
    });

    return suggestions;
  }

  /**
   * Determine layer type from layer name
   */
  private getLayerType(layerName: string): string {
    const name = layerName.toLowerCase();

    if (name.includes('face') || name.includes('expression') || name.includes('mouth')) return 'face';
    if (name.includes('eye') || name.includes('eyes') || name.includes('eye color')) return 'eyes';
    if (name.includes('hair')) return 'hair';
    if (name.includes('hat') || name.includes('cap') || name.includes('helmet')) return 'hat';
    if (name.includes('cloth') || name.includes('shirt') || name.includes('dress')) return 'clothing';
    if (name.includes('accessory') || name.includes('jewelry') || name.includes('necklace')) return 'accessory';
    if (name.includes('background') || name.includes('bg')) return 'background';
    if (name.includes('decoration') || name.includes('effect')) return 'decoration';

    return 'other';
  }

  /**
   * Check if emotion should match between face and other layer
   */
  private shouldEmotionMatch(emotion: string, layerType: string): { match: boolean; confidence: number; reason: string } {
    switch (layerType) {
      case 'eyes':
        return { match: true, confidence: 0.9, reason: 'Eye expression coordination' };
      case 'accessory':
        if (['angry', 'fierce'].includes(emotion)) {
          return { match: true, confidence: 0.7, reason: 'Aggressive accessories for fierce expressions' };
        }
        if (['happy', 'smile'].includes(emotion)) {
          return { match: true, confidence: 0.6, reason: 'Cheerful accessories for happy expressions' };
        }
        return { match: false, confidence: 0, reason: '' };
      case 'clothing':
        if (['formal', 'elegant'].includes(emotion)) {
          return { match: true, confidence: 0.5, reason: 'Formal clothing for elegant expressions' };
        }
        return { match: false, confidence: 0, reason: '' };
      default:
        return { match: false, confidence: 0, reason: '' };
    }
  }

  /**
   * Check color harmony between layers
   */
  private getColorHarmony(color: string, triggerLayerName: string, targetLayerName: string): { shouldMatch: boolean; confidence: number } {
    const triggerType = this.getLayerType(triggerLayerName);
    const targetType = this.getLayerType(targetLayerName);

    // Hair and eyes often coordinate
    if ((triggerType === 'hair' && targetType === 'eyes') || (triggerType === 'eyes' && targetType === 'hair')) {
      return { shouldMatch: true, confidence: 0.7 };
    }

    // Clothing and accessories coordinate
    if ((triggerType === 'clothing' && targetType === 'accessory') || (triggerType === 'accessory' && targetType === 'clothing')) {
      return { shouldMatch: true, confidence: 0.8 };
    }

    // Background rarely coordinates with character features
    if (triggerType === 'background' || targetType === 'background') {
      return { shouldMatch: false, confidence: 0 };
    }

    return { shouldMatch: false, confidence: 0 };
  }

  /**
   * Get filename without extension
   */
  private getFileNameWithoutExtension(fileName: string): string {
    return fileName.replace(/\.(png|jpg|jpeg|gif|svg|webp|tiff|tif)$/i, '');
  }

  /**
   * Calculate confidence score for a rule suggestion
   */
  private calculateConfidence(relationship: RelationshipGroup, triggerLayer: any, targetLayer: any): number {
    let confidence = 0.5; // Base confidence

    // More layers involved = higher confidence
    confidence += (relationship.layers.length - 2) * 0.1;

    // More files involved = higher confidence
    confidence += Math.min(relationship.totalFiles / 10, 0.3);

    // Face expressions are usually good triggers
    if (triggerLayer.layerName.toLowerCase().includes('face')) {
      confidence += 0.2;
    }

    // Common keywords get higher confidence
    const commonKeywords = ['angry', 'happy', 'sad', 'surprised', 'bubble', 'kiss'];
    if (commonKeywords.includes(relationship.keyword.toLowerCase())) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }
}

// Create analyzer instance for export
export const simpleRelationshipAnalyzer = new SimpleRelationshipAnalyzer();

// Analyze imported files function
export function analyzeImportedFiles(files: FileList) {
  return simpleRelationshipAnalyzer.analyzeImportedFiles(files);
}
