/**
 * Unified Image Processing Enhancements
 * 
 * Enhanced image processing capabilities for the unified image service:
 * - WebP and AVIF format handling
 * - Large image processing
 * - Advanced compression algorithms
 */

import { 
  ImageProcessingOptions,
  ProcessedImage,
  SupportedInputFormat,
  SupportedOutputFormat
} from './image-types';

import {
  getMimeTypeFromFormat,
  calculateOptimalImageSize,
  blobToArrayBuffer,
  isWebPSupported,
  isAVIFSupported,
  getOptimalImageFormat
} from './image-utils';

import { ErrorType, AppError } from '@/services/error';

/**
 * Utility function to handle errors in async functions
 * This replaces the @handleErrors decorator which can't be used with standalone functions
 * 
 * @param fn The function to wrap with error handling
 * @param errorType The type of error to create
 * @param errorMessage The error message to use
 * @returns The wrapped function
 */
function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  errorType: ErrorType,
  errorMessage: string
): T {
  return (async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    try {
      return await fn(...args);
    } catch (error) {
      // Create a proper AppError
      if (error instanceof AppError) {
        throw error;
      }
      
      // Create a new AppError with the provided type and message
      const appError = new AppError(
        errorMessage,
        errorType,
        {
          originalError: error,
          details: { args: JSON.stringify(args) }
        }
      );
      
      console.error(appError.message, appError);
      throw appError;
    }
  }) as T;
}

/**
 * Convert an image to WebP format with optimized settings
 * 
 * @param image HTMLImageElement or canvas to convert
 * @param options Processing options including quality
 * @returns Promise with WebP data URL
 */
async function convertToWebP_Internal(
  image: HTMLImageElement | HTMLCanvasElement,
  options: ImageProcessingOptions = {}
): Promise<string> {
  try {
    // Create canvas if input is an image
    let canvas: HTMLCanvasElement;
    let width: number;
    let height: number;
    
    if (image instanceof HTMLImageElement) {
      width = image.naturalWidth;
      height = image.naturalHeight;
      
      // Apply resizing if specified in options
      if (options.maxWidth || options.maxHeight || options.maxDimension) {
        const { width: newWidth, height: newHeight } = calculateOptimalImageSize(
          width,
          height,
          options.maxWidth || options.maxDimension || width,
          options.maxHeight || options.maxDimension || height
        );
        width = newWidth;
        height = newHeight;
      }
      
      // Create canvas and draw image
      canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Failed to get canvas context');
      
      ctx.imageSmoothingEnabled = true;
      // @ts-ignore - some browsers don't support this property
      ctx.imageSmoothingQuality = 'high';
      ctx.drawImage(image, 0, 0, width, height);
    } else {
      // Input is already a canvas
      canvas = image;
      width = canvas.width;
      height = canvas.height;
    }
    
    // Determine quality setting
    const quality = options.quality !== undefined ? options.quality : 0.9;
    
    // Use lossless compression mode if specified
    const isLossless = options.compression === 'lossless';
    const webpQuality = isLossless ? 1.0 : quality;
    
    // Convert to WebP
    const webpDataUrl = canvas.toDataURL('image/webp', webpQuality);
    
    return webpDataUrl;
  } catch (error) {
    throw error;
  }
}

/**
 * Convert an image to AVIF format with optimized settings
 * 
 * @param image HTMLImageElement or canvas to convert
 * @param options Processing options including quality
 * @returns Promise with AVIF data URL or fallback to WebP if AVIF fails
 */
async function convertToAVIF_Internal(
  image: HTMLImageElement | HTMLCanvasElement,
  options: ImageProcessingOptions = {}
): Promise<string> {
  try {
    // Check browser support first
    if (!isAVIFSupported()) {
      console.log('AVIF format not supported, falling back to WebP');
      return convertToWebP(image, options);
    }
    
    // Create canvas if input is an image
    let canvas: HTMLCanvasElement;
    let width: number;
    let height: number;
    
    if (image instanceof HTMLImageElement) {
      width = image.naturalWidth;
      height = image.naturalHeight;
      
      // Apply resizing if specified in options
      if (options.maxWidth || options.maxHeight || options.maxDimension) {
        const { width: newWidth, height: newHeight } = calculateOptimalImageSize(
          width,
          height,
          options.maxWidth || options.maxDimension || width,
          options.maxHeight || options.maxDimension || height
        );
        width = newWidth;
        height = newHeight;
      }
      
      // Create canvas and draw image
      canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Failed to get canvas context');
      
      ctx.imageSmoothingEnabled = true;
      // @ts-ignore - some browsers don't support this property
      ctx.imageSmoothingQuality = 'high';
      ctx.drawImage(image, 0, 0, width, height);
    } else {
      // Input is already a canvas
      canvas = image;
      width = canvas.width;
      height = canvas.height;
    }
    
    // Determine quality setting - AVIF works best with slightly lower quality
    // since its visual quality is better at equivalent compression levels
    const quality = options.quality !== undefined ? options.quality : 0.85;
    
    try {
      // Convert to AVIF - might fail in some browsers
      // @ts-ignore - AVIF may not be in TypeScript definitions
      const avifDataUrl = canvas.toDataURL('image/avif', quality);
      
      // Verify that we got an AVIF image back (some browsers return other formats silently)
      if (!avifDataUrl.startsWith('data:image/avif')) {
        console.warn('Browser reported AVIF support but returned different format, falling back to WebP');
        return convertToWebP(image, options);
      }
      
      return avifDataUrl;
    } catch (avifError) {
      console.warn('AVIF encoding failed, falling back to WebP:', avifError);
      return convertToWebP(image, options);
    }
  } catch (error) {
    throw error;
  }
}

/**
 * Analyze an image and determine if it has transparency
 * 
 * @param imageData Image data to analyze
 * @param sampleSize Number of pixels to sample
 * @returns True if image has transparency
 */
export function detectTransparency(
  imageData: ImageData,
  sampleSize: number = 1000
): boolean {
  const pixels = imageData.data;
  const pixelCount = pixels.length / 4; // RGBA = 4 bytes per pixel
  
  // For small images, check all pixels
  if (pixelCount <= sampleSize) {
    for (let i = 3; i < pixels.length; i += 4) {
      if (pixels[i] < 254) { // Allow for small rounding errors
        return true;
      }
    }
    return false;
  }
  
  // For larger images, sample pixels
  const step = Math.max(1, Math.floor(pixelCount / sampleSize));
  
  for (let i = 3; i < pixels.length; i += 4 * step) {
    if (pixels[i] < 254) {
      return true;
    }
  }
  
  return false;
}

/**
 * Detect image type based on content analysis
 * Used to determine optimal compression parameters
 * 
 * @param canvas Canvas containing the image
 * @returns Image type classification
 */
export function detectImageType(
  canvas: HTMLCanvasElement
): 'photo' | 'graphic' | 'text' | 'mixed' {
  try {
    const ctx = canvas.getContext('2d');
    if (!ctx) return 'mixed';
    
    // Get image data for analysis
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    
    // Metrics for classifying image type
    let edgeCount = 0;
    const colorVariety = new Set<string>();
    let sharpEdgeCount = 0;
    let smoothTransitionCount = 0;
    
    // Sample pixels across the image (not all pixels to improve performance)
    const sampleWidth = Math.min(canvas.width, 100);
    const sampleHeight = Math.min(canvas.height, 100);
    const stepX = Math.max(1, Math.floor(canvas.width / sampleWidth));
    const stepY = Math.max(1, Math.floor(canvas.height / sampleHeight));
    
    // Calculate color distribution and edge metrics
    for (let y = 0; y < canvas.height - stepY; y += stepY) {
      for (let x = 0; x < canvas.width - stepX; x += stepX) {
        const idx = (y * canvas.width + x) * 4;
        const idxRight = (y * canvas.width + (x + stepX)) * 4;
        const idxBelow = ((y + stepY) * canvas.width + x) * 4;
        
        // Track color variety (simplified to RGB components)
        const colorKey = `${data[idx]},${data[idx+1]},${data[idx+2]}`;
        colorVariety.add(colorKey);
        
        // Detect horizontal edges
        const diffRight = Math.abs(data[idx] - data[idxRight]) + 
                         Math.abs(data[idx+1] - data[idxRight+1]) + 
                         Math.abs(data[idx+2] - data[idxRight+2]);
        
        // Detect vertical edges
        const diffBelow = Math.abs(data[idx] - data[idxBelow]) + 
                         Math.abs(data[idx+1] - data[idxBelow+1]) + 
                         Math.abs(data[idx+2] - data[idxBelow+2]);
        
        // Count edges based on difference threshold
        if (diffRight > 100 || diffBelow > 100) {
          edgeCount++;
          
          // Count sharp edges (very high contrast)
          if (diffRight > 200 || diffBelow > 200) {
            sharpEdgeCount++;
          }
        } else if (diffRight > 30 || diffBelow > 30) {
          // Count smooth transitions
          smoothTransitionCount++;
        }
      }
    }
    
    // Normalize metrics by sample size
    const totalSamples = (canvas.width / stepX) * (canvas.height / stepY);
    const edgeRatio = edgeCount / totalSamples;
    const sharpEdgeRatio = sharpEdgeCount / totalSamples;
    const colorVarietyRatio = colorVariety.size / totalSamples;
    const smoothTransitionRatio = smoothTransitionCount / totalSamples;
    
    // Classification based on metrics
    if (sharpEdgeRatio > 0.15 && colorVarietyRatio < 0.1) {
      return 'text'; // High contrast, low color variety = text
    } else if (edgeRatio > 0.1 && colorVarietyRatio < 0.3) {
      return 'graphic'; // Meaningful edges but limited colors = graphic/illustration
    } else if (smoothTransitionRatio > 0.3 && colorVarietyRatio > 0.2) {
      return 'photo'; // Smooth transitions and varied colors = photo
    } else {
      return 'mixed'; // No clear classification
    }
  } catch (error) {
    console.warn('Error detecting image type:', error);
    return 'mixed'; // Default fallback
  }
}

/**
 * Process modern formats like WebP and AVIF with optimized handling
 */
async function processModernFormat_Internal(
  file: File,
  format: SupportedInputFormat,
  options: ImageProcessingOptions,
  layerName?: string,
  traitName?: string
): Promise<ProcessedImage> {
  const startTime = Date.now();
  
  // SECURITY FIX: Use data URL instead of blob URL
  const dataUrl = await new Promise<string>((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = () => reject(reader.error);
    reader.readAsDataURL(file);
  });
  
  try {
    // Load the image to get dimensions
    const img = await loadImageFromURL(dataUrl);
    
    // Calculate optimal dimensions based on options
    const { width, height } = calculateOptimalImageSize(
      img.naturalWidth,
      img.naturalHeight,
      options.maxWidth || options.maxDimension || 2048,
      options.maxHeight || options.maxDimension || 2048,
      8000000 // 8 megapixels max for modern formats
    );
    
    // Create a canvas with the optimal dimensions
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    
    // Get context with alpha support
    const ctx = canvas.getContext('2d', { alpha: true, willReadFrequently: false });
    if (!ctx) {
      throw new Error('Failed to get canvas context');
    }
    
    // Draw the image with high quality
    ctx.imageSmoothingEnabled = true;
    // @ts-ignore - some browsers don't support this property
    ctx.imageSmoothingQuality = 'high';
    ctx.drawImage(img, 0, 0, width, height);
    
    // Determine optimal output format
    let outputFormat: SupportedOutputFormat = options.outputFormat || 'png';
    const quality = options.quality || 0.9;
    
    // If input is already WebP/AVIF, keep it that way unless specified otherwise
    if ((format === 'webp' || format === 'avif') && !options.outputFormat) {
      // Use a temporary variable to avoid reassigning to const
      const formatToUse = format as SupportedOutputFormat;
      outputFormat = formatToUse;
    }
    
    // Use optimal compression strategies based on format
    let dataUrl: string;
    
    if (outputFormat === 'webp') {
      // WebP supports both lossy and lossless compression
      const isLossless = options.compression === 'lossless';
      
      // Higher quality for lossless
      const webpQuality = isLossless ? 1.0 : quality;
      dataUrl = canvas.toDataURL('image/webp', webpQuality);
    } 
    else if (outputFormat === 'avif' && supportsAVIF()) {
      // AVIF for browsers that support it
      // @ts-ignore - AVIF may not be in TypeScript definitions
      dataUrl = canvas.toDataURL('image/avif', quality);
    } 
    else if (outputFormat === 'png' || hasTransparency(canvas)) {
      // Use PNG for transparent images
      dataUrl = canvas.toDataURL('image/png');
    } 
    else {
      // Fall back to JPEG for non-transparent images
      dataUrl = canvas.toDataURL('image/jpeg', quality);
    }
    
    return {
      width,
      height,
      url: dataUrl,
      processingTime: Date.now() - startTime,
      format: outputFormat,
      originalName: file.name,
      byteSize: dataUrl.length
    };
  } finally {
    // SECURITY FIX: No cleanup needed for data URLs
    // Data URLs don't need to be revoked like blob URLs
  }
}

/**
 * Process large files with chunked/streaming approach
 */
async function processLargeFile_Internal(
  file: File,
  format: SupportedInputFormat | null,
  options: ImageProcessingOptions,
  layerName?: string,
  traitName?: string
): Promise<ProcessedImage> {
  const startTime = Date.now();
  const mimeType = file.type || (format ? getMimeTypeFromFormat(format) : 'image/png');
  
  // SECURITY FIX: Use data URL instead of blob URL
  const dataUrl = await new Promise<string>((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = () => reject(reader.error);
    reader.readAsDataURL(file);
  });

  try {
    // Get image dimensions first to plan processing
    const img = await loadImageFromURL(dataUrl);
    
    // Check if image is extremely large (over 20MP)
    const megapixels = (img.naturalWidth * img.naturalHeight) / 1000000;
    const isExtremelyLarge = megapixels > 20;
    
    // For extremely large images, use multi-step downsampling
    if (isExtremelyLarge) {
      return processExtremelyLargeImage(img, file, format, options, layerName, traitName);
    }
    
    // Calculate optimal dimensions for large but manageable images
    const { width, height } = calculateOptimalImageSize(
      img.naturalWidth,
      img.naturalHeight,
      options.maxWidth || options.maxDimension || 2048,
      options.maxHeight || options.maxDimension || 2048,
      6000000 // 6 megapixels max for large files
    );
    
    // Create an appropriately sized canvas
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    
    // Get context with optimized settings
    const ctx = canvas.getContext('2d', { 
      alpha: format === 'png' || format === 'webp' || format === 'gif',
      willReadFrequently: false,
      // @ts-ignore - For browsers that support it
      desynchronized: true
    });
    
    if (!ctx) {
      throw new Error('Failed to get canvas context');
    }
    
    // Draw the image
    ctx.imageSmoothingEnabled = true;
    // @ts-ignore - some browsers don't support this property
    ctx.imageSmoothingQuality = 'high';
    ctx.drawImage(img, 0, 0, width, height);
    
    // Determine best output format for the large image
    const outputFormat: SupportedOutputFormat = options.outputFormat || 'jpg';
    const quality = options.quality || 0.85; // Lower default quality for large images
    
    // AVIF is best for large images if supported
    if (supportsAVIF() && (outputFormat === 'avif' || !options.outputFormat)) {
      // @ts-ignore - AVIF may not be in TypeScript definitions
      const avifDataUrl = canvas.toDataURL('image/avif', quality);
      
      // If AVIF is more than 30% smaller, use it
      const jpegDataUrl = canvas.toDataURL('image/jpeg', quality);
      if (avifDataUrl.length < jpegDataUrl.length * 0.7) {
        return {
          width,
          height,
          url: avifDataUrl,
          processingTime: Date.now() - startTime,
          format: 'avif',
          originalName: file.name,
          byteSize: avifDataUrl.length
        };
      }
    }
    
    // Check if image has transparency
    if (hasTransparency(canvas)) {
      // Use WebP for transparent images if supported, otherwise PNG
      if (supportsWebP()) {
        const webpDataUrl = canvas.toDataURL('image/webp', 0.9);
        return {
          width,
          height,
          url: webpDataUrl,
          processingTime: Date.now() - startTime,
          format: 'webp',
          originalName: file.name,
          byteSize: webpDataUrl.length
        };
      } else {
        const pngDataUrl = canvas.toDataURL('image/png');
        return {
          width,
          height,
          url: pngDataUrl,
          processingTime: Date.now() - startTime,
          format: 'png',
          originalName: file.name,
          byteSize: pngDataUrl.length
        };
      }
    }
    
    // For non-transparent images, use JPEG with optimized quality
    const jpegDataUrl = canvas.toDataURL('image/jpeg', quality);
    return {
      width,
      height,
      url: jpegDataUrl,
      processingTime: Date.now() - startTime,
      format: 'jpg',
      originalName: file.name,
      byteSize: jpegDataUrl.length
    };
  } finally {
    // SECURITY FIX: No cleanup needed for data URLs
    // Data URLs don't need to be revoked like blob URLs
  }
}

/**
 * Process extremely large images (>20MP) with multi-step downsampling
 * to prevent browser crashes and out-of-memory errors
 */
async function processExtremelyLargeImage(
  img: HTMLImageElement,
  file: File,
  format: SupportedInputFormat | null,
  options: ImageProcessingOptions,
  layerName?: string,
  traitName?: string
): Promise<ProcessedImage> {
  const startTime = Date.now();
  
  // Calculate target dimensions
  const targetDimensions = calculateOptimalImageSize(
    img.naturalWidth,
    img.naturalHeight,
    options.maxWidth || options.maxDimension || 2048,
    options.maxHeight || options.maxDimension || 2048,
    4000000 // 4 megapixels max for final result
  );
  
  // Calculate intermediate dimensions - progressive downsampling
  // First step - reduce to 1/4 the original pixels (1/2 each dimension)
  const step1Width = Math.floor(img.naturalWidth / 2);
  const step1Height = Math.floor(img.naturalHeight / 2);
  
  // Create first canvas for initial downsampling
  const canvas1 = document.createElement('canvas');
  canvas1.width = step1Width;
  canvas1.height = step1Height;
  
  // Draw first pass
  const ctx1 = canvas1.getContext('2d', { alpha: true, willReadFrequently: false });
  if (!ctx1) {
    throw new Error('Failed to get canvas context');
  }
  ctx1.imageSmoothingEnabled = true;
  // @ts-ignore - some browsers don't support this property
  ctx1.imageSmoothingQuality = 'high';
  ctx1.drawImage(img, 0, 0, step1Width, step1Height);
  
  // Second pass - draw to final dimensions
  const canvas2 = document.createElement('canvas');
  canvas2.width = targetDimensions.width;
  canvas2.height = targetDimensions.height;
  
  const ctx2 = canvas2.getContext('2d', { alpha: true, willReadFrequently: false });
  if (!ctx2) {
    throw new Error('Failed to get second canvas context');
  }
  ctx2.imageSmoothingEnabled = true;
  // @ts-ignore - some browsers don't support this property
  ctx2.imageSmoothingQuality = 'high';
  ctx2.drawImage(canvas1, 0, 0, targetDimensions.width, targetDimensions.height);
  
  // Free first canvas memory
  canvas1.width = 1;
  canvas1.height = 1;
  
  // Apply advanced compression based on format
  let outputFormat: SupportedOutputFormat = options.outputFormat || 'jpg';
  let dataUrl: string;
  
  if (hasTransparency(canvas2)) {
    if (supportsWebP()) {
      dataUrl = canvas2.toDataURL('image/webp', 0.85);
      outputFormat = 'webp';
    } else {
      dataUrl = canvas2.toDataURL('image/png');
      outputFormat = 'png';
    }
  } else if (supportsAVIF()) {
    // @ts-ignore - AVIF may not be in TypeScript definitions
    dataUrl = canvas2.toDataURL('image/avif', 0.8);
    outputFormat = 'avif';
  } else {
    // Use JPEG for non-transparent images with adaptive quality
    dataUrl = canvas2.toDataURL('image/jpeg', 0.85);
    outputFormat = 'jpg';
  }
  
  // Free second canvas memory
  canvas2.width = 1;
  canvas2.height = 1;
  
  return {
    width: targetDimensions.width,
    height: targetDimensions.height,
    url: dataUrl,
    processingTime: Date.now() - startTime,
    format: outputFormat,
    originalName: file.name,
    byteSize: dataUrl.length
  };
}

/**
 * Check if image has transparency
 */
function hasTransparency(canvas: HTMLCanvasElement): boolean {
  try {
    const ctx = canvas.getContext('2d');
    if (!ctx) return false;
    
    // Sample pixels across the image to check for transparency
    const sampleSize = Math.max(1, Math.floor(Math.min(canvas.width, canvas.height) / 10));
    
    for (let x = 0; x < canvas.width; x += sampleSize) {
      for (let y = 0; y < canvas.height; y += sampleSize) {
        const pixel = ctx.getImageData(x, y, 1, 1).data;
        if (pixel[3] < 255) { // Alpha channel < 255 means transparency
          return true;
        }
      }
    }
    
    return false;
  } catch (error) {
    console.warn('Error checking for transparency:', error);
    return false; // Assume no transparency on error
  }
}

/**
 * Load an image from a URL
 */
function loadImageFromURL(url: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error(`Failed to load image from ${url}`));
    img.src = url;
  });
}

/**
 * Feature detection for WebP support
 */
function supportsWebP(): boolean {
  // Import the utility function from image-utils
  return isWebPSupported();
}

/**
 * Feature detection for AVIF support
 */
function supportsAVIF(): boolean {
  // Import the utility function from image-utils
  return isAVIFSupported();
}

// Wrap the functions with error handling and export them
export const convertToWebP = withErrorHandling(
  convertToWebP_Internal,
  ErrorType.PROCESSING,
  'Failed to convert image to WebP'
);

export const convertToAVIF = withErrorHandling(
  convertToAVIF_Internal,
  ErrorType.PROCESSING,
  'Failed to convert image to AVIF'
);

export const processModernFormat = withErrorHandling(
  processModernFormat_Internal,
  ErrorType.PROCESSING,
  'Failed to process modern format image'
);

export const processLargeFile = withErrorHandling(
  processLargeFile_Internal,
  ErrorType.PROCESSING,
  'Failed to process large image'
);

// The processExtremelyLargeImage function is only used internally
