/**
 * Dosya dönüştürme yardımcı fonksiyonları
 * 
 * Bu modül, dosya formatları arasında dönüştürme için ortak yardımcı fonksiyonları içerir.
 */

/**
 * Dosyayı Data URL'ye dönüştürür
 * @param file Dönüştürülecek dosya
 * @returns Promise<string> Data URL
 */
export function fileToDataURL(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = () => {
      resolve(reader.result as string);
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to convert file to Data URL'));
    };
    
    reader.readAsDataURL(file);
  });
}

/**
 * Data URL'yi Blob'a dönüştürür
 * @param dataURL Dönüştürülecek Data URL
 * @returns Blob
 */
export function dataURLtoBlob(dataURL: string): Blob {
  // Data URL formatı: data:[<mediatype>][;base64],<data>
  const arr = dataURL.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'application/octet-stream';
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  
  return new Blob([u8arr], { type: mime });
}

/**
 * Data URL'yi File'a dönüştürür
 * @param dataURL Dönüştürülecek Data URL
 * @param fileName Dosya adı
 * @returns File
 */
export function dataURLtoFile(dataURL: string, fileName: string): File {
  const blob = dataURLtoBlob(dataURL);
  return new File([blob], fileName, { type: blob.type });
}

/**
 * Blob'u Data URL'ye dönüştürür
 * @param blob Dönüştürülecek Blob
 * @returns Promise<string> Data URL
 */
export function blobToDataURL(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = () => {
      resolve(reader.result as string);
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to convert blob to Data URL'));
    };
    
    reader.readAsDataURL(blob);
  });
}

/**
 * SECURITY FIX: Convert file to data URL instead of blob URL
 * @param file File to convert
 * @returns Promise<string> Data URL
 */
export function fileToDataURL(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = () => reject(reader.error);
    reader.readAsDataURL(file);
  });
}

/**
 * Blob URL'yi Data URL'ye dönüştürür
 * @param blobURL Dönüştürülecek Blob URL
 * @returns Promise<string> Data URL
 */
export function blobURLtoDataURL(blobURL: string): Promise<string> {
  return new Promise((resolve, reject) => {
    fetch(blobURL)
      .then(response => response.blob())
      .then(blob => blobToDataURL(blob))
      .then(dataURL => resolve(dataURL))
      .catch(error => reject(error));
  });
}

/**
 * Görüntüyü Data URL'ye dönüştürür
 * @param image Dönüştürülecek görüntü
 * @param type MIME tipi (varsayılan: 'image/png')
 * @param quality Görüntü kalitesi (0-1 arası, varsayılan: 0.9)
 * @returns Data URL
 */
export function imageToDataURL(
  image: HTMLImageElement,
  type: string = 'image/png',
  quality: number = 0.9
): string {
  const canvas = document.createElement('canvas');
  canvas.width = image.width;
  canvas.height = image.height;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    throw new Error('Failed to get canvas context');
  }
  
  ctx.drawImage(image, 0, 0);
  return canvas.toDataURL(type, quality);
}

/**
 * ArrayBuffer'ı Base64 string'e dönüştürür
 * @param buffer Dönüştürülecek ArrayBuffer
 * @returns Base64 string
 */
export function arrayBufferToBase64(buffer: ArrayBuffer): string {
  let binary = '';
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;
  
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  
  return btoa(binary);
}

/**
 * Base64 string'i ArrayBuffer'a dönüştürür
 * @param base64 Dönüştürülecek Base64 string
 * @returns ArrayBuffer
 */
export function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binaryString = atob(base64);
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  
  return bytes.buffer;
}
