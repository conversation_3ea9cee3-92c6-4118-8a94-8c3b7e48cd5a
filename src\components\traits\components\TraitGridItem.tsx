import React from 'react';
import { Box, Paper, Typography, Slider } from '@mui/material';
import { alpha, useTheme } from '@mui/material/styles';
import { TraitRarityLockControl } from '@/components/rarity';
import { EditableRarityValue } from './';
import { Trait } from '@/types';

interface TraitGridItemProps {
  trait: Trait;
  isSelected: boolean;
  layerId: string;
  onSelect: () => void;
  onRarityChange: (value: number) => void;
  onRarityChangeCommitted: (value: number) => void;
}

const TraitGridItem: React.FC<TraitGridItemProps> = ({
  trait,
  isSelected,
  layerId,
  onSelect,
  onRarityChange,
  onRarityChangeCommitted
}) => {
  const theme = useTheme();

  return (
    <Paper
      elevation={isSelected ? 2 : 0}
      onClick={onSelect}
      sx={{
        p: 0.5,
        display: 'flex',
        flexDirection: 'column',
        height: 'auto',
        minHeight: '180px',
        width: '100%',
        cursor: 'pointer',
        border: isSelected ? '2px solid' : '1px solid',
        borderColor: isSelected ? 'primary.main' : 'divider',
        boxShadow: isSelected ? 2 : 0,
        bgcolor: isSelected ? alpha(theme.palette.primary.main, 0.08) : 'background.paper',
        position: 'relative',
        borderRadius: '8px',
        '&:hover': {
          bgcolor: 'action.hover',
          boxShadow: 1
        },
        transition: 'all 0.2s ease',
        overflow: 'hidden',
        boxSizing: 'border-box'
      }}
    >
      {/* Image Container */}
      <Box sx={{
        width: '100%',
        position: 'relative',
        paddingTop: '100%', // 1:1 square ratio
        mb: 0.5,
        overflow: 'hidden',
        borderRadius: 1,
        bgcolor: 'background.paper',
        boxShadow: 'inset 0 0 5px rgba(0,0,0,0.1)'
      }}>
        <Box sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <img
            src={trait.imageUrl || ''}
            alt={trait.name}
            style={{
              maxWidth: '90%',
              maxHeight: '90%',
              objectFit: 'contain',
              borderRadius: 4
            }}
            onError={(e) => {
              console.log('Image load error for trait:', trait.name, 'URL:', trait.imageUrl);
              // Try to reload from persistent storage
              if (trait.imageUrl && trait.imageUrl.startsWith('blob:')) {
                // Blob URL might be invalid, try to regenerate
                import('@/services/memory/image-persistence.service').then(({ getTraitImage }) => {
                  getTraitImage(trait.layerId || 'unknown', trait.name).then(newUrl => {
                    if (newUrl && e.target) {
                      (e.target as HTMLImageElement).src = newUrl;
                    }
                  });
                });
              }
            }}
          />
        </Box>
      </Box>

      {/* Trait Name */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'center',
        my: 0.5,
        px: 0.5,
        minHeight: '24px',
        width: '100%',
        flexShrink: 0,
        overflow: 'hidden'
      }}>
        <Typography
          variant="body2"
          sx={{
            fontWeight: isSelected ? 'bold' : 'normal',
            maxWidth: '100%',
            flex: 1,
            lineHeight: 1.1,
            fontSize: '0.75rem',
            display: '-webkit-box',
            WebkitLineClamp: 1,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            wordBreak: 'break-word',
            textAlign: 'center'
          }}
        >
          {trait.name}
        </Typography>
      </Box>

      {/* Rarity Controls */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          my: 0.5,
          px: 0.5,
          zIndex: 5,
          minHeight: '40px',
          overflow: 'hidden'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Rarity display and slider */}
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          width: '100%',
          mb: 0.5
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="caption" sx={{ mr: 1, color: 'text.secondary' }}>
              Rarity:
            </Typography>
            <TraitRarityLockControl
              traitId={trait.id}
              layerId={layerId}
              isLocked={trait.rarityLocked}
              size="small"
            />
          </Box>

          {/* Editable rarity value */}
          <EditableRarityValue
            rarity={trait.rarity}
            isLocked={trait.rarityLocked}
            isSelected={isSelected}
            onChange={onRarityChangeCommitted}
          />
        </Box>

        {/* Slider - disabled when trait is locked */}
        <Slider
          size="small"
          value={trait.rarity}
          min={0.1}
          max={100}
          step={0.1}
          disabled={trait.rarityLocked}
          onChange={(_, value) => onRarityChange(Number(value.toFixed(2)))}
          onChangeCommitted={(_, value) => onRarityChangeCommitted(Number(value.toFixed(2)))}
          valueLabelDisplay="auto"
          valueLabelFormat={(value) => `${value.toFixed(2)}%`}
          sx={{
            width: '100%',
            mt: 0.5,
            '& .MuiSlider-thumb': {
              width: 12,
              height: 12,
              '&:hover,&.Mui-focusVisible': {
                boxShadow: '0px 0px 0px 8px rgba(25, 118, 210, 0.16)'
              }
            },
            '& .MuiSlider-rail': {
              height: 3
            },
            '& .MuiSlider-track': {
              height: 3
            },
            zIndex: 10
          }}
          aria-label={`${trait.name} rarity percentage`}
          aria-valuetext={`${trait.rarity.toFixed(2)}%`}
        />
      </Box>
    </Paper>
  );
};

export default React.memo(TraitGridItem);