import { Rule, RuleCondition } from '../types/Rules.types'

/**
 * Rules Engine for V2 - Applies rules during trait selection
 */

export interface TraitSelection {
  [layerId: string]: string // layerId -> traitId
}

export interface RuleEvaluationResult {
  isValid: boolean
  appliedRules: string[]
  violations: string[]
  suggestions: TraitSelection
}

/**
 * Evaluate rules against current trait selection
 */
export const evaluateRules = (
  currentSelection: TraitSelection,
  rules: Rule[],
  layers: any[]
): RuleEvaluationResult => {
  const result: RuleEvaluationResult = {
    isValid: true,
    appliedRules: [],
    violations: [],
    suggestions: { ...currentSelection }
  }

  // Filter enabled rules and sort by order
  const enabledRules = rules
    .filter(rule => rule.enabled)
    .sort((a, b) => a.order - b.order)

  // Process IF_THEN rules
  for (const rule of enabledRules) {
    if (rule.type === 'IF_THEN') {
      const evaluation = evaluateIfThenRule(rule, currentSelection, result.suggestions, layers)

      if (evaluation.applied) {
        result.appliedRules.push(rule.name)
      }

      if (evaluation.violation) {
        result.isValid = false
        result.violations.push(evaluation.violation)
      }

      // Always apply suggestions if they exist (even with violations)
      if (evaluation.suggestion) {
        Object.assign(result.suggestions, evaluation.suggestion)
        currentSelection = { ...result.suggestions }
      }
    }
  }

  return result
}

/**
 * Evaluate a single IF_THEN rule
 */
const evaluateIfThenRule = (
  rule: Rule,
  currentSelection: TraitSelection,
  suggestions: TraitSelection,
  layers?: any[]
) => {
  const result = {
    applied: false,
    violation: null as string | null,
    suggestion: null as TraitSelection | null
  }

  if (!rule.conditions || rule.conditions.length < 2) {
    return result
  }

  // Separate IF and THEN conditions
  const ifConditions = rule.conditions.filter(c => c.type === 'IF')
  const thenConditions = rule.conditions.filter(c => c.type === 'THEN')

  if (ifConditions.length === 0 || thenConditions.length === 0) {
    return result
  }

  // Check IF conditions
  const ifConditionsMet = evaluateIfConditions(ifConditions, currentSelection)

  if (ifConditionsMet) {
    // Group THEN conditions by target layer for proper handling
    const thenConditionsByLayer = new Map<string, RuleCondition[]>()

    for (const thenCondition of thenConditions) {
      const targetLayerId = thenCondition.targetLayerId || thenCondition.layerId
      if (!targetLayerId) continue

      if (!thenConditionsByLayer.has(targetLayerId)) {
        thenConditionsByLayer.set(targetLayerId, [])
      }
      thenConditionsByLayer.get(targetLayerId)!.push(thenCondition)
    }

    // Apply THEN conditions layer by layer
    for (const [targetLayerId, layerConditions] of thenConditionsByLayer) {
      const firstCondition = layerConditions[0]
      const operator = firstCondition.operator

      if (operator === 'CANNOT_HAVE') {
        // Handle CANNOT_HAVE conditions
        const targetTraitId = firstCondition.targetTraitId || firstCondition.traitId

        if (targetTraitId && currentSelection[targetLayerId] === targetTraitId) {
          // Specific trait is forbidden and currently selected
          result.violation = `Rule "${rule.name}": ${getLayerName(targetLayerId, layers)} cannot have ${getTraitName(targetTraitId, layers)} when ${formatIfConditions(ifConditions, layers)}`
          // Remove the forbidden trait
          result.suggestion = { ...suggestions }
          delete result.suggestion[targetLayerId]
          result.applied = true
        } else if (!targetTraitId) {
          // Cannot have any trait in this layer
          if (currentSelection[targetLayerId]) {
            result.violation = `Rule "${rule.name}": ${getLayerName(targetLayerId, layers)} cannot have any trait when ${formatIfConditions(ifConditions, layers)}`
            result.suggestion = { ...suggestions }
            delete result.suggestion[targetLayerId]
            result.applied = true
          }
        }
      } else if (operator === 'MUST_HAVE') {
        // Handle MUST_HAVE conditions using shared logic
        const mustHaveResult = applyMustHaveConditions(layerConditions, currentSelection, suggestions, layers)
        if (mustHaveResult.applied && mustHaveResult.suggestion) {
          result.suggestion = mustHaveResult.suggestion
          result.applied = true
        }
      }
    }
  }

  return result
}

/**
 * Evaluate IF conditions (supports AND/OR logic)
 */
const evaluateIfConditions = (
  conditions: RuleCondition[],
  currentSelection: TraitSelection
): boolean => {
  if (conditions.length === 0) return false
  if (conditions.length === 1) {
    const condition = conditions[0]
    const { layerId, traitId } = condition

    if (!layerId) return false

    if (!traitId) {
      // "Any trait" condition - check if layer has any selection
      return !!currentSelection[layerId]
    }

    // Specific trait condition
    return currentSelection[layerId] === traitId
  }

  // Multiple conditions - check for OR/AND logic
  let hasOr = false
  let hasAnd = false

  for (let i = 1; i < conditions.length; i++) {
    const operator = conditions[i].logicalOperator || 'AND'
    if (operator === 'OR') hasOr = true
    if (operator === 'AND') hasAnd = true
  }

  // Simple case: all OR operators
  if (hasOr && !hasAnd) {
    const result = conditions.some(condition => {
      const { layerId, traitId } = condition
      if (!layerId) return false
      const conditionMet = !traitId ? !!currentSelection[layerId] : currentSelection[layerId] === traitId

      return conditionMet
    })
    return result
  }

  // Simple case: all AND operators (or mixed - default to AND precedence)
  return conditions.every(condition => {
    const { layerId, traitId } = condition
    if (!layerId) return false
    return !traitId ? !!currentSelection[layerId] : currentSelection[layerId] === traitId
  })
}

/**
 * Apply rules to a trait selection and return corrected selection
 */
export const applyRulesToSelection = (
  selection: TraitSelection,
  rules: Rule[],
  layers: any[]
): TraitSelection => {
  const evaluation = evaluateRules(selection, rules, layers)
  return evaluation.suggestions
}

/**
 * Check if a trait selection is valid according to rules
 */
export const isSelectionValid = (
  selection: TraitSelection,
  rules: Rule[],
  layers: any[]
): boolean => {
  const evaluation = evaluateRules(selection, rules, layers)
  return evaluation.isValid
}

/**
 * Get rule violations for a selection
 */
export const getRuleViolations = (
  selection: TraitSelection,
  rules: Rule[],
  layers: any[]
): string[] => {
  const evaluation = evaluateRules(selection, rules, layers)
  return evaluation.violations
}

// Helper functions
const getLayerName = (layerId: string, layers?: any[]): string => {
  if (layers) {
    const layer = layers.find(l => l.id === layerId)
    if (layer && layer.name) {
      return layer.name
    }
  }
  // Fallback to formatted ID
  return layerId.charAt(0).toUpperCase() + layerId.slice(1)
}

const getTraitName = (traitId: string, layers?: any[]): string => {
  if (layers) {
    for (const layer of layers) {
      if (layer.traits) {
        const trait = layer.traits.find((t: any) => t.id === traitId)
        if (trait && trait.name) {
          return trait.name
        }
      }
    }
  }
  // Fallback to formatted ID
  return traitId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatIfConditions = (conditions: RuleCondition[], layers?: any[]): string => {
  return conditions.map(c => {
    const layerName = getLayerName(c.layerId || '', layers)
    const traitName = c.traitId ? getTraitName(c.traitId, layers) : 'any trait'
    return `${layerName} has ${traitName}`
  }).join(' and ')
}

/**
 * Check if a layer should be skipped due to CANNOT_HAVE rules
 */
const shouldLayerBeSkipped = (
  layerId: string,
  currentSelection: TraitSelection,
  rules: Rule[],
  layers?: any[]
): boolean => {
  for (const rule of rules.filter(r => r.enabled && r.type === 'IF_THEN')) {
    const ifConditions = rule.conditions.filter(c => c.type === 'IF')
    const thenConditions = rule.conditions.filter(c => c.type === 'THEN')

    // Check if IF conditions are met
    const ifConditionsMet = evaluateIfConditions(ifConditions, currentSelection)

    if (ifConditionsMet) {
      // Check if any THEN condition is CANNOT_HAVE for this layer
      for (const thenCondition of thenConditions) {
        const targetLayerId = thenCondition.targetLayerId || thenCondition.layerId
        const targetTraitId = thenCondition.targetTraitId || thenCondition.traitId
        const operator = thenCondition.operator

        if (targetLayerId === layerId && operator === 'CANNOT_HAVE' && !targetTraitId) {
          // This layer cannot have any trait
          return true
        }
      }
    }
  }

  return false
}

/**
 * Apply only MUST_HAVE rules to selection (not CANNOT_HAVE)
 */
const applyMustHaveRulesToSelection = (
  selection: TraitSelection,
  rules: Rule[],
  layers?: any[]
): TraitSelection => {
  let result = { ...selection }

  for (const rule of rules.filter(r => r.enabled && r.type === 'IF_THEN')) {
    const ifConditions = rule.conditions.filter(c => c.type === 'IF')
    const thenConditions = rule.conditions.filter(c => c.type === 'THEN')

    if (ifConditions.length === 0 || thenConditions.length === 0) continue

    // Check if IF conditions are met
    const ifConditionsMet = evaluateIfConditions(ifConditions, result)

    if (ifConditionsMet) {
      // Apply MUST_HAVE conditions only
      const mustHaveResult = applyMustHaveConditions(thenConditions, result, result, layers)
      if (mustHaveResult.applied && mustHaveResult.suggestion) {
        result = mustHaveResult.suggestion
      }
    }
  }

  return result
}

/**
 * Apply MUST_HAVE conditions from THEN conditions
 */
const applyMustHaveConditions = (
  thenConditions: RuleCondition[],
  currentSelection: TraitSelection,
  suggestions: TraitSelection,
  layers?: any[]
): { applied: boolean; suggestion: TraitSelection | null } => {
  const result = {
    applied: false,
    suggestion: null as TraitSelection | null
  }

  // Only apply MUST_HAVE conditions, skip CANNOT_HAVE
  const mustHaveConditions = thenConditions.filter(c => c.operator === 'MUST_HAVE')
  if (mustHaveConditions.length === 0) return result

  // Group MUST_HAVE conditions by target layer
  const mustHaveByLayer = new Map<string, RuleCondition[]>()

  for (const condition of mustHaveConditions) {
    const targetLayerId = condition.targetLayerId || condition.layerId
    if (!targetLayerId) continue

    if (!mustHaveByLayer.has(targetLayerId)) {
      mustHaveByLayer.set(targetLayerId, [])
    }
    mustHaveByLayer.get(targetLayerId)!.push(condition)
  }

  let newSuggestions = { ...suggestions }

  // Apply MUST_HAVE conditions layer by layer
  for (const [targetLayerId, layerConditions] of mustHaveByLayer) {
    if (layerConditions.length === 1) {
      // Single MUST_HAVE condition
      const targetTraitId = layerConditions[0].targetTraitId || layerConditions[0].traitId
      if (targetTraitId && currentSelection[targetLayerId] !== targetTraitId) {
        newSuggestions[targetLayerId] = targetTraitId
        result.applied = true
      }
    } else {
      // Multiple MUST_HAVE conditions - select one based on rarity
      const validTraitIds = layerConditions
        .map(c => c.targetTraitId || c.traitId)
        .filter(Boolean) as string[]

      if (validTraitIds.length > 0 && !validTraitIds.includes(currentSelection[targetLayerId])) {
        // Find the layer and traits for rarity-based selection
        const targetLayer = layers?.find(l => l.id === targetLayerId)
        if (targetLayer && targetLayer.traits) {
          const validTraits = targetLayer.traits.filter((t: any) => validTraitIds.includes(t.id))

          if (validTraits.length > 0) {
            // Select based on rarity
            const totalRarity = validTraits.reduce((sum: number, trait: any) => sum + trait.rarity, 0)
            const random = Math.random() * totalRarity

            let accumulator = 0
            for (const trait of validTraits) {
              accumulator += trait.rarity
              if (random <= accumulator) {
                newSuggestions[targetLayerId] = trait.id
                result.applied = true
                break
              }
            }
          }
        } else {
          // Fallback: select first valid trait if no layer info available
          newSuggestions[targetLayerId] = validTraitIds[0]
          result.applied = true
        }
      }
    }
  }

  if (result.applied) {
    result.suggestion = newSuggestions
  }

  return result
}

/**
 * Get dependency-aware layer processing order
 * IF condition layers should be processed before THEN target layers
 */
const getDependencyAwareLayerOrder = (layers: any[], rules: Rule[]): any[] => {
  // Start with original order
  const sortedLayers = [...layers].sort((a, b) => (a.order || 0) - (b.order || 0))

  // Build dependency map: targetLayerId -> [ifConditionLayerIds]
  const dependencies = new Map<string, Set<string>>()

  for (const rule of rules.filter(r => r.enabled && r.type === 'IF_THEN')) {
    const ifConditions = rule.conditions.filter(c => c.type === 'IF')
    const thenConditions = rule.conditions.filter(c => c.type === 'THEN')

    for (const thenCondition of thenConditions) {
      const targetLayerId = thenCondition.targetLayerId || thenCondition.layerId
      if (!targetLayerId) continue

      if (!dependencies.has(targetLayerId)) {
        dependencies.set(targetLayerId, new Set())
      }

      for (const ifCondition of ifConditions) {
        if (ifCondition.layerId) {
          dependencies.get(targetLayerId)!.add(ifCondition.layerId)
        }
      }
    }
  }

  // Topological sort to respect dependencies
  const result: any[] = []
  const visited = new Set<string>()
  const visiting = new Set<string>()

  const visit = (layer: any) => {
    if (visiting.has(layer.id)) {
      // Circular dependency - use original order
      return
    }
    if (visited.has(layer.id)) {
      return
    }

    visiting.add(layer.id)

    // Visit dependencies first (IF condition layers)
    const deps = dependencies.get(layer.id) || new Set()
    for (const depLayerId of deps) {
      const depLayer = sortedLayers.find(l => l.id === depLayerId)
      if (depLayer) {
        visit(depLayer)
      }
    }

    visiting.delete(layer.id)
    visited.add(layer.id)
    result.push(layer)
  }

  // Visit all layers
  for (const layer of sortedLayers) {
    visit(layer)
  }

  return result
}

export const generateRandomSelectionWithRules = (
  layers: any[],
  rules: Rule[],
  maxAttempts: number = 100
): TraitSelection => {
  // Get dependency-aware layer order
  const sortedLayers = getDependencyAwareLayerOrder(layers, rules)

  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    // Generate selection layer by layer, applying rules after each layer
    const selection: TraitSelection = {}

    for (const layer of sortedLayers) {
      if (layer.traits && layer.traits.length > 0) {
        // Check if this layer should be skipped due to CANNOT_HAVE rules
        const shouldSkipLayer = shouldLayerBeSkipped(layer.id, selection, rules, layers)

        if (shouldSkipLayer) {
          continue
        }

        // Select random trait based on rarity
        const totalRarity = layer.traits.reduce((sum: number, trait: any) => sum + trait.rarity, 0)
        const random = Math.random() * totalRarity

        let accumulator = 0
        for (const trait of layer.traits) {
          accumulator += trait.rarity
          if (random <= accumulator) {
            selection[layer.id] = trait.id
            break
          }
        }

        // Apply MUST_HAVE rules after each layer selection (but not CANNOT_HAVE)
        const correctedSelection = applyMustHaveRulesToSelection(selection, rules, layers)
        Object.assign(selection, correctedSelection)
      }
    }

    // Check if final selection is valid
    const isValid = isSelectionValid(selection, rules, layers)

    if (isValid) {
      return selection
    }
  }

  // If we can't generate a valid selection, return empty selection
  console.warn('❌ Could not generate valid selection with rules after', maxAttempts, 'attempts')
  return {}
}
