/**
 * Unified Image Service
 *
 * A consolidated image processing implementation that provides:
 * - Consistent image loading, manipulation and optimization
 * - Support for multiple image formats including TIFF
 * - Optimized processing with web workers when available
 * - Memory-efficient handling of large images
 * - Placeholder image generation
 * - Batch processing capabilities
 *
 * This service replaces:
 * - src/utils/fileUtils.ts image processing methods
 * - src/utils/image/tiffUtils.ts
 * - src/services/image-processing.service.ts
 * - Duplicate processImageUrl functions
 */

import {
  ImageSource,
  ImageProcessingOptions,
  SupportedInputFormat,
  SupportedOutputFormat,
  ProcessedImage,
  PlaceholderOptions,
  BatchProcessItem,
  ImageCapabilities,
  DEFAULT_PROCESSING_OPTIONS,
  SUPPORTED_INPUT_FORMATS,
  SUPPORTED_OUTPUT_FORMATS
} from './image-types';

import {
  EnhancedConversionOptions,
  EnhancedConversionResult,
  convertToUnifiedOptions
} from './enhanced-conversion-types';

import {
  getMimeTypeFromFormat,
  getFormatFromFileName,
  getFormatFromDataUrl,
  getFormatFromMimeType,
  createImageCacheKey,
  createPlaceholderImageDataURL,
  createErrorPlaceholderImage,
  blobToArrayBuffer,
  calculateOptimalImageSize,
  formatBytes,
  isFormatSupported,
  cleanFileName,
  isWebPSupported,
  isAVIFSupported,
  getOptimalImageFormat
} from './image-utils';

import {
  imageCache,
  cacheImage,
  isImageCached,
  getCachedImage,
  clearImageCache
} from './image-cache';

import {
  processTiffImage,
  processTiffFile,
  processTiffDataUrl,
  isValidTiffData
} from './tiff-handler';

import {
  unifiedErrorService,
  AppError,
  ErrorType,
  ErrorSeverity,
  handleErrors,
  withRetry
} from '@/services/error';

import { convertToWebP, convertToAVIF, detectImageType, detectTransparency } from './unified-image-enhancement';
import { imageScaling, ImageScalingOptions, ScaledImageResult, ScalingQuality } from './image-scaling';

/**
 * Main Unified Image Service Class
 */
export class UnifiedImageService {
  // Feature detection flags
  private hasWasmSupport = typeof WebAssembly === 'object';
  private hasWorkerSupport = typeof Worker !== 'undefined';
  private hasGpuSupport = false;
  private maxTextureSize = 4096; // Default conservative value

  /**
   * Initialize the image service
   */
  constructor() {
    // Detect hardware capabilities
    this.detectCapabilities().catch(err => {
      console.warn('Error detecting hardware capabilities:', err);
    });
  }

  /**
   * Detect available hardware and browser capabilities
   */
  private async detectCapabilities(): Promise<void> {
    try {
      // Check for GPU compute capabilities
      this.hasGpuSupport = await this.detectGpuSupport();

      // Detect maximum texture size
      this.maxTextureSize = await this.detectMaxTextureSize();

      console.log('Image capabilities detected:', {
        wasm: this.hasWasmSupport,
        workers: this.hasWorkerSupport,
        gpu: this.hasGpuSupport,
        maxTextureSize: this.maxTextureSize
      });
    } catch (error) {
      console.error('Failed to detect capabilities:', error);
    }
  }

  /**
   * Detect if GPU acceleration is available
   */
  private async detectGpuSupport(): Promise<boolean> {
    try {
      // Check for WebGPU API
      // @ts-ignore - WebGPU not in all TypeScript definitions yet
      if ('gpu' in navigator) {
        // @ts-ignore
        const adapter = await navigator.gpu?.requestAdapter();
        if (adapter) {
          return true;
        }
      }

      // Fallback check for WebGL 2.0
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2');

      if (gl) {
        // Check for compute capabilities
        const extensions = [
          'WEBGL_compute_shader',
          'EXT_compute_shader',
          'EXT_texture_filter_anisotropic'
        ];

        // If any of these extensions exist, consider GPU supported
        for (const ext of extensions) {
          if (gl.getExtension(ext)) {
            return true;
          }
        }

        // Basic WebGL 2 is still useful
        return true;
      }

      return false;
    } catch (error) {
      console.warn('GPU support detection failed:', error);
      return false;
    }
  }

  /**
   * Detect maximum texture size supported by GPU
   */
  private async detectMaxTextureSize(): Promise<number> {
    try {
      // Try WebGL2 first
      const canvas = document.createElement('canvas');
      const gl2 = canvas.getContext('webgl2');

      if (gl2) {
        const maxSize = gl2.getParameter(gl2.MAX_TEXTURE_SIZE);
        return typeof maxSize === 'number' ? maxSize : 4096;
      }

      // Fallback to WebGL1
      const gl1 = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

      if (gl1) {
        const maxSize = gl1.getParameter(gl1.MAX_TEXTURE_SIZE);
        return typeof maxSize === 'number' ? maxSize : 4096;
      }

      // Conservative default
      return 4096;
    } catch (error) {
      console.warn('Error detecting max texture size:', error);
      return 4096;
    }
  }

  /**
   * Get capabilities information
   */
  public getCapabilities(): ImageCapabilities {
    return {
      wasmSupport: this.hasWasmSupport,
      gpuSupport: this.hasGpuSupport,
      webWorkerSupport: this.hasWorkerSupport,
      supportedInputFormats: SUPPORTED_INPUT_FORMATS,
      supportedOutputFormats: SUPPORTED_OUTPUT_FORMATS,
      maxTextureSize: this.maxTextureSize
    };
  }

  /**
   * Process an image from any source
   */
  @handleErrors(ErrorType.PROCESSING, 'Failed to process image')
  public async processImage(
    source: ImageSource,
    options: ImageProcessingOptions = {},
    layerName?: string,
    traitName?: string
  ): Promise<ProcessedImage> {
    const startTime = Date.now();

    const mergedOptions = { ...DEFAULT_PROCESSING_OPTIONS, ...options };

    try {
      // Determine source type and format
      let sourceFormat: SupportedInputFormat | null = null;
      let arrayBuffer: ArrayBuffer | null = null;
      let sourceFile: File | null = null;

      // Handle different source types
      if (source instanceof File || source instanceof Blob) {
        // File or Blob
        sourceFile = source instanceof File ? source : new File([source], 'image.bin', { type: source.type });
        sourceFormat = this.getImageFormat(sourceFile);

        // Generate cache key early to check cache
        const cacheKey = createImageCacheKey(
          sourceFile.name + sourceFile.size + sourceFile.lastModified,
          mergedOptions,
          layerName || sourceFile.name,
          traitName || ''
        );

        // Check cache
        if (isImageCached(cacheKey)) {
          const cached = getCachedImage(cacheKey);
          if (cached) {
            if (typeof cached === 'string') {
              return {
                width: 0,
                height: 0,
                url: cached,
                processingTime: 0,
                originalName: sourceFile.name,
                format: sourceFormat || 'unknown'
              };
            } else {
              return cached as ProcessedImage;
            }
          }
        }

        // Handle TIFF files specially
        if (sourceFormat === 'tiff' || sourceFormat === 'tif') {
          return processTiffFile(sourceFile, mergedOptions, layerName, traitName);
        }

        // Process in main thread

        // Fallback to main thread processing
        const dataUrl = await this.fileToDataURL(sourceFile, mergedOptions);

        const result: ProcessedImage = {
          width: 0, // We don't know dimensions without loading the image
          height: 0,
          url: dataUrl,
          processingTime: Date.now() - startTime,
          originalName: sourceFile.name,
          format: sourceFormat || 'unknown',
          byteSize: dataUrl.length
        };

        // Cache the result
        cacheImage(cacheKey, result);

        return result;
      } else if (source instanceof ArrayBuffer) {
        // ArrayBuffer - try to determine format by examining content
        arrayBuffer = source;

        // Check if it's a TIFF
        if (isValidTiffData(arrayBuffer)) {
          sourceFormat = 'tiff';
          return processTiffImage(arrayBuffer, mergedOptions, layerName, traitName);
        }

        // For other formats, convert to blob then data URL
        const blob = new Blob([arrayBuffer]);
        sourceFormat = this.getImageFormat(blob) || 'png';

        // Process in main thread

        // Fallback to main thread processing
        const dataUrl = await this.blobToDataURL(blob, mergedOptions);

        return {
          width: 0,
          height: 0,
          url: dataUrl,
          processingTime: Date.now() - startTime,
          format: sourceFormat,
          buffer: arrayBuffer,
          byteSize: arrayBuffer.byteLength
        };
      } else if (typeof source === 'string') {
        // String - could be URL or data URL
        if (source.startsWith('data:')) {
          // Data URL
          sourceFormat = getFormatFromDataUrl(source);

          // Handle TIFF data URLs specially
          if (source.startsWith('data:image/tiff;base64,')) {
            return processTiffDataUrl(source, mergedOptions, layerName, traitName);
          }

          // Cache key for data URLs
          const cacheKey = createImageCacheKey(
            source.substring(0, 100) + source.length,
            mergedOptions,
            layerName || '',
            traitName || ''
          );

          // Check cache
          if (isImageCached(cacheKey)) {
            const cached = getCachedImage(cacheKey);
            if (cached) {
              return typeof cached === 'string' ?
                { width: 0, height: 0, url: cached, processingTime: 0, format: sourceFormat || 'unknown' } :
                cached as ProcessedImage;
            }
          }

          // Process in main thread

          // For data URLs without processing options, use directly but record metrics
          return {
            width: 0,
            height: 0,
            url: source,
            processingTime: Date.now() - startTime,
            format: sourceFormat || 'unknown',
            byteSize: source.length
          };
        } else {
          // URL - fetch the image
          try {
            // Extract format from URL
            sourceFormat = getFormatFromFileName(source) || 'png';

            // Cache key for URLs
            const cacheKey = createImageCacheKey(
              source,
              mergedOptions,
              layerName || '',
              traitName || ''
            );

            // Check cache first
            if (isImageCached(cacheKey)) {
              const cached = getCachedImage(cacheKey);
              if (cached) {
                return typeof cached === 'string' ?
                  { width: 0, height: 0, url: cached, processingTime: 0, format: sourceFormat || 'unknown' } :
                  cached as ProcessedImage;
              }
            }

            // Add cache-busting for network URLs
            const fetchUrl = source.startsWith('http') ?
              `${source}${source.includes('?') ? '&' : '?'}t=${Date.now()}` :
              source;

            // Fetch the image with timeout
            const response = await this.fetchWithTimeout(fetchUrl, mergedOptions.timeout || 10000);

            if (!response.ok) {
              throw new AppError(
                `Failed to fetch image: ${response.status} ${response.statusText}`,
                ErrorType.NETWORK,
                ErrorSeverity.WARNING,
                null,
                { url: source, status: response.status }
              );
            }

            // Get as blob
            const blob = await response.blob();

            // Handle TIFFs specially
            if (sourceFormat === 'tiff' || sourceFormat === 'tif' ||
                blob.type === 'image/tiff') {
              const buffer = await blobToArrayBuffer(blob);
              return processTiffImage(buffer, mergedOptions, layerName, traitName);
            }

            // Process in main thread

            // Fallback to main thread processing
            const dataUrl = await this.blobToDataURL(blob, mergedOptions);

            const result: ProcessedImage = {
              width: 0,
              height: 0,
              url: dataUrl,
              processingTime: Date.now() - startTime,
              format: sourceFormat,
              byteSize: dataUrl.length
            };

            // Cache the result
            cacheImage(cacheKey, result);

            return result;
          } catch (error) {
            // Create placeholder for errors if layer/trait info provided
            if (layerName || traitName) {
              const placeholder = createPlaceholderImageDataURL(
                layerName || 'Error',
                traitName || 'Loading Error'
              );

              return {
                width: 200,
                height: 200,
                url: placeholder,
                processingTime: Date.now() - startTime,
                format: 'svg'
              };
            }

            // Re-throw
            throw error;
          }
        }
      }

      // If we reach here, we have an unsupported source type
      throw new AppError(
        'Unsupported image source type',
        ErrorType.VALIDATION,
        ErrorSeverity.ERROR,
        null,
        { sourceType: typeof source }
      );
    } catch (error) {
      // Create placeholder for errors when possible
      if (layerName || traitName) {
        const placeholder = createPlaceholderImageDataURL(
          layerName || 'Error',
          traitName || 'Processing Error',
          { isError: true }
        );

        return {
          width: 200,
          height: 200,
          url: placeholder,
          processingTime: Date.now() - startTime,
          format: 'svg'
        };
      }

      // End performance tracking with error
      performanceTrackingService.endTracking(trackingId, {
        status: 'error',
        error: error instanceof Error ? error.message : String(error)
      });

      // Re-throw
      throw error;
    } finally {
      // End performance tracking if not already ended
      performanceTrackingService.endTracking(trackingId, {
        processingTime: Date.now() - startTime
      });
    }
  }

  /**
   * Process multiple images in a batch
   */
  @handleErrors(ErrorType.PROCESSING, 'Failed to process image batch')
  public async processBatch(
    items: BatchProcessItem[],
    globalOptions: ImageProcessingOptions = {}
  ): Promise<ProcessedImage[]> {
    const startTime = Date.now();
    // Start performance tracking
    const trackingId = performanceTrackingService.startTracking('processBatch', 'image', {
      itemCount: items.length,
      options: { ...globalOptions },
      useWorker: this.hasWorkerSupport && !globalOptions.disableWorker
    });

    const results: ProcessedImage[] = [];
    const mergedGlobalOptions = { ...DEFAULT_PROCESSING_OPTIONS, ...globalOptions };

    try {
      // Determine optimal batch size based on item count and options
      const BATCH_SIZE = this.determineBatchSize(items.length, mergedGlobalOptions);

      // Process in chunks
      for (let i = 0; i < items.length; i += BATCH_SIZE) {
        const chunk = items.slice(i, i + BATCH_SIZE);

        // Process each chunk in parallel
        const chunkPromises = chunk.map(item => {
          // Merge global options with item-specific options
          const itemOptions = { ...mergedGlobalOptions, ...item.options };

          // Process the item
          return this.processImage(
            item.data,
            itemOptions,
            item.layerName,
            item.traitName
          ).catch(error => {
            // Return placeholder on error
            unifiedErrorService.reportError(error, {
              type: ErrorType.PROCESSING,
              context: {
                operation: 'processBatch',
                item: item.layerName || 'unknown',
                index: i + chunk.indexOf(item)
              }
            });

            return {
              width: 200,
              height: 200,
              url: createErrorPlaceholderImage(
                item.layerName || 'Error',
                item.traitName || 'Batch Processing Error'
              ),
              processingTime: 0,
              format: 'svg'
            };
          });
        });

        // Wait for the chunk to complete
        const chunkResults = await Promise.all(chunkPromises);
        results.push(...chunkResults);

        // Report progress
        if (mergedGlobalOptions.onProgress) {
          const progress = Math.min(100, Math.round((i + chunk.length) / items.length * 100));
          mergedGlobalOptions.onProgress(progress);
        }

        // Give UI thread a chance to update
        await new Promise<void>(resolve => setTimeout(resolve, 0));
      }

      // Log performance metrics
      const totalTime = Date.now() - startTime;
      const avgTime = totalTime / items.length;

      console.log(`Batch processing complete: ${items.length} images in ${totalTime}ms (avg: ${avgTime.toFixed(1)}ms per image)`);

      // End performance tracking with success
      performanceTrackingService.endTracking(trackingId, {
        status: 'success',
        itemCount: items.length,
        totalTime,
        averageTime: avgTime,
        resultCount: results.length
      });

      return results;
    } catch (error) {
      // End performance tracking with error
      performanceTrackingService.endTracking(trackingId, {
        status: 'error',
        error: error instanceof Error ? error.message : String(error),
        itemCount: items.length,
        processingTime: Date.now() - startTime
      });

      // Log error and re-throw
      console.error('Error in batch processing:', error);
      throw error;
    }
  }

  /**
   * Create a placeholder image with customized appearance
   */
  public createPlaceholder(
    layerName: string = 'Layer',
    traitName: string = 'Trait',
    options: PlaceholderOptions = {}
  ): string {
    return createPlaceholderImageDataURL(layerName, traitName, options);
  }

  /**
   * Create an error placeholder image
   */
  public createErrorPlaceholder(
    layerName: string = 'Error',
    traitName: string = 'Error',
    fileName: string = 'unknown'
  ): string {
    return createErrorPlaceholderImage(layerName, traitName, fileName);
  }

  /**
   * Convert a file to a data URL with optimized handling
   * Supports modern formats (WebP, AVIF) based on browser capability and options
   */
  @handleErrors(ErrorType.PROCESSING, 'Failed to convert file to data URL')
  @withRetry({
    maxRetries: 2,
    initialDelay: 500,
    retryCondition: (error) => {
      // Only retry network or timeout errors
      return error.message.includes('timeout') ||
             error.message.includes('network') ||
             error.message.includes('abort');
    }
  })
  public async fileToDataURL(
    file: File,
    options: ImageProcessingOptions = {}
  ): Promise<string> {
    // For very small files, use FileReader directly (unless format conversion is requested)
    if (file.size < 1024 * 1024 &&
        !options.useModernFormats &&
        !options.outputFormat &&
        !options.maxWidth &&
        !options.maxHeight &&
        !options.maxDimension) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = () => {
          if (typeof reader.result === 'string') {
            resolve(reader.result);
          } else {
            reject(new Error('FileReader did not return a string'));
          }
        };

        reader.onerror = () => {
          reject(new Error(`Error reading file: ${file.name}`));
        };

        reader.readAsDataURL(file);
      });
    }

    // SECURITY FIX: Use FileReader instead of blob URLs for canvas processing
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      // Set a timeout for loading
      const timeout = setTimeout(() => {
        reject(new AppError(
          `Image loading timeout: ${file.name}`,
          ErrorType.TIMEOUT,
          ErrorSeverity.WARNING,
          null,
          { fileName: file.name, fileSize: file.size }
        ));
      }, options.timeout || 15000);

      reader.onload = () => {
        clearTimeout(timeout);

        if (typeof reader.result === 'string') {
          // Create an image element with data URL
          const img = new Image();

          img.onload = () => {

        try {
          // Calculate dimensions
          const { width, height } = calculateOptimalImageSize(
            img.width,
            img.height,
            options.maxWidth || options.maxDimension || 800,
            options.maxHeight || options.maxDimension || 800,
            4000000 // 4 megapixels max
          );

          // Create a canvas
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;

          // Get context
          const ctx = canvas.getContext('2d', {
            alpha: true,
            willReadFrequently: false
          });

          if (!ctx) {
            throw new Error('Could not get canvas context');
          }

          // Draw the image with high quality
          ctx.imageSmoothingEnabled = true;
          // @ts-ignore
          ctx.imageSmoothingQuality = 'high';
          ctx.drawImage(img, 0, 0, width, height);

          // Analyze image content for optimal format selection
          const hasAlpha = hasTransparency(canvas);
          const imageType = detectImageType(canvas);

          // Determine optimal output format based on options and browser support
          let dataUrl: string;
          const quality = options.quality || (imageType === 'photo' ? 0.85 : 0.9);

          // Explicit format specified in options takes precedence
          if (options.outputFormat) {
            // Use specified format
            switch (options.outputFormat) {
              case 'avif':
                if (isAVIFSupported()) {
                  dataUrl = convertToAVIF(canvas, { quality });
                } else if (isWebPSupported()) {
                  console.log('AVIF requested but not supported, using WebP');
                  dataUrl = convertToWebP(canvas, { quality });
                } else {
                  dataUrl = hasAlpha ?
                    canvas.toDataURL('image/png') :
                    canvas.toDataURL('image/jpeg', quality);
                }
                break;

              case 'webp':
                if (isWebPSupported()) {
                  dataUrl = convertToWebP(canvas, { quality });
                } else {
                  dataUrl = hasAlpha ?
                    canvas.toDataURL('image/png') :
                    canvas.toDataURL('image/jpeg', quality);
                }
                break;

              case 'png':
                dataUrl = canvas.toDataURL('image/png');
                break;

              case 'jpg':
              case 'jpeg':
                dataUrl = canvas.toDataURL('image/jpeg', quality);
                break;

              default:
                dataUrl = canvas.toDataURL(`image/${options.outputFormat}`, quality);
            }
          }
          // Use modern formats if enabled and supported
          else if (options.useModernFormats) {
            // Use transparency info to help select format
            if (options.preserveTransparency && hasAlpha) {
              // For transparent images, use WebP if supported, otherwise PNG
              if (isWebPSupported()) {
                dataUrl = convertToWebP(canvas, {
                  quality: quality,
                  compression: 'lossy'
                });
              } else {
                dataUrl = canvas.toDataURL('image/png');
              }
            }
            else {
              // For non-transparent images or when transparency isn't required
              if (options.preferredFormat === 'avif' && isAVIFSupported()) {
                dataUrl = convertToAVIF(canvas, { quality: quality });
              }
              else if ((options.preferredFormat === 'webp' || options.preferredFormat === 'auto') && isWebPSupported()) {
                dataUrl = convertToWebP(canvas, { quality: quality });
              }
              else if (options.preferredFormat === 'auto' && isAVIFSupported()) {
                dataUrl = convertToAVIF(canvas, { quality: quality });
              }
              else {
                // Fallback to JPEG for non-transparent when modern formats not supported
                dataUrl = canvas.toDataURL('image/jpeg', quality);
              }
            }
          }
          // Fallback to standard formats based on content
          else {
            if (file.type.includes('png') || file.type.includes('svg') || hasAlpha) {
              // Use PNG for transparent images or when source was PNG/SVG
              dataUrl = canvas.toDataURL('image/png');
            } else {
              // Use JPEG for non-transparent photos
              dataUrl = canvas.toDataURL('image/jpeg', quality);
            }
          }

          // Return as Promise
          if (dataUrl instanceof Promise) {
            dataUrl.then(resolve).catch(reject);
          } else {
            resolve(dataUrl);
          }
        } catch (error) {
          reject(error);
        }
      };

      // Handle load errors
      img.onerror = () => {
        clearTimeout(timeout);
        reject(new Error(`Failed to load image: ${file.name}`));
      };

      // Start loading with data URL
      img.src = reader.result;
        } else {
          reject(new Error('FileReader did not return a string'));
        }
      };

      reader.onerror = () => {
        clearTimeout(timeout);
        reject(new Error(`Failed to read file: ${file.name}`));
      };

      // Start reading the file
      reader.readAsDataURL(file);
    });
  }

  /**
   * Check if canvas contains transparent pixels
   */
  private hasTransparency(canvas: HTMLCanvasElement): boolean {
    try {
      const ctx = canvas.getContext('2d');
      if (!ctx) return false;

      // Sample image data for transparency
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      return detectTransparency(imageData);
    } catch (error) {
      console.warn('Error checking for transparency:', error);
      return false;
    }
  }

  /**
   * Convert a blob to a data URL
   */
  @handleErrors(ErrorType.PROCESSING, 'Failed to convert blob to data URL')
  public async blobToDataURL(
    blob: Blob,
    options: ImageProcessingOptions = {}
  ): Promise<string> {
    // For small blobs, use FileReader directly
    if (blob.size < 1024 * 1024) { // Less than 1MB
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = () => {
          if (typeof reader.result === 'string') {
            resolve(reader.result);
          } else {
            reject(new Error('FileReader did not return a string'));
          }
        };

        reader.onerror = () => {
          reject(new Error('Error reading blob'));
        };

        reader.readAsDataURL(blob);
      });
    }

    // For larger blobs, create a fake file and use the file method
    const fakeFile = new File([blob], 'image.' + (blob.type.split('/')[1] || 'bin'), {
      type: blob.type
    });

    return this.fileToDataURL(fakeFile, options);
  }

  /**
   * Convert a blob URL to a data URL
   */
  @handleErrors(ErrorType.PROCESSING, 'Failed to convert blob URL to data URL')
  public async blobUrlToDataURL(
    blobUrl: string,
    options: ImageProcessingOptions = {}
  ): Promise<string> {
    if (!blobUrl.startsWith('blob:')) {
      return blobUrl; // Not a blob URL, return as-is
    }

    try {
      // Fetch the blob
      const response = await fetch(blobUrl);

      if (!response.ok) {
        throw new Error(`Failed to fetch blob URL: ${response.status} ${response.statusText}`);
      }

      // Get as blob
      const blob = await response.blob();

      // Convert to data URL
      return this.blobToDataURL(blob, options);
    } finally {
      // Always try to clean up the blob URL
      try {
        URL.revokeObjectURL(blobUrl);
      } catch (e) {
        // Ignore errors revoking URLs
      }
    }
  }

  /**
   * Create an ImageBitmap from an ArrayBuffer
   */
  @handleErrors(ErrorType.PROCESSING, 'Failed to create ImageBitmap from buffer')
  public async createImageFromBuffer(
    buffer: ArrayBuffer,
    width: number,
    height: number
  ): Promise<ImageBitmap> {
    // Create an ImageData object
    const imageData = new ImageData(
      new Uint8ClampedArray(buffer),
      width,
      height
    );

    // Create a temporary canvas
    const canvas = new OffscreenCanvas(width, height);
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('Failed to create canvas context');
    }

    // Draw the image data
    ctx.putImageData(imageData, 0, 0);

    // Create an ImageBitmap
    return createImageBitmap(canvas);
  }

  /**
   * Clean the image cache
   */
  public clearCache(): void {
    clearImageCache();
  }

  /**
   * Get the current cache statistics
   */
  public getCacheStats(): any {
    return imageCache.getStats();
  }

  /**
   * Determine image format from File or Blob
   */
  private getImageFormat(file: File | Blob): SupportedInputFormat | null {
    // Check MIME type first
    if (file.type) {
      const formatFromMime = getFormatFromMimeType(file.type);
      if (formatFromMime) return formatFromMime;
    }

    // If File, check filename
    if (file instanceof File) {
      return getFormatFromFileName(file.name);
    }

    // Fallback to generic type
    return null;
  }

  /**
   * Determine optimal batch size based on item count and options
   */
  private determineBatchSize(itemCount: number, options: ImageProcessingOptions): number {
    // Base batch size on total items
    if (itemCount <= 5) return 5;           // For very few items, process all at once
    if (itemCount <= 20) return 5;          // For small batches, use 5
    if (itemCount <= 50) return 5;          // For medium batches, use 5
    if (itemCount <= 100) return 4;         // For large batches, use 4
    if (itemCount <= 500) return 3;         // For very large batches, use 3
    return 2;                              // For enormous batches, use 2
  }

  /**
   * Fetch with timeout
   */
  private async fetchWithTimeout(
    url: string,
    timeoutMs: number = 10000
  ): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

    try {
      const response = await fetch(url, {
        signal: controller.signal,
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      });

      return response;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * Scale an image with optimized settings
   * @param imageData - Source image data
   * @param sourceFormat - Source image format
   * @param options - Scaling options
   * @returns Promise with scaled image result
   */
  @handleErrors(ErrorType.PROCESSING, 'Failed to scale image')
  public async scaleImage(
    imageData: ArrayBuffer | File | Blob | string,
    sourceFormat: string,
    options: ImageScalingOptions = {}
  ): Promise<ScaledImageResult> {
    try {
      // Convert to ArrayBuffer if needed
      let buffer: ArrayBuffer;

      if (imageData instanceof File || imageData instanceof Blob) {
        buffer = await blobToArrayBuffer(imageData);
      } else if (typeof imageData === 'string') {
        // Convert data URL to ArrayBuffer
        if (imageData.startsWith('data:')) {
          const response = await fetch(imageData);
          buffer = await response.arrayBuffer();
        } else {
          // Fetch URL
          const response = await this.fetchWithTimeout(imageData);
          buffer = await response.arrayBuffer();
        }
      } else {
        buffer = imageData;
      }

      // Use the image scaling service
      return imageScaling.scaleImage(buffer, sourceFormat, options);
    } catch (error) {
      console.error('Image scaling error:', error);
      throw error;
    }
  }

  /**
   * Batch scale multiple images
   * @param images - Array of image data and formats
   * @param options - Scaling options
   * @returns Promise with array of scaled results
   */
  @handleErrors(ErrorType.PROCESSING, 'Failed to scale image batch')
  public async scaleBatch(
    images: Array<{ data: ArrayBuffer | File | Blob | string; format: string; filename?: string }>,
    options: ImageScalingOptions = {}
  ): Promise<ScaledImageResult[]> {
    try {
      // Convert all images to ArrayBuffer
      const bufferImages = await Promise.all(
        images.map(async (image) => {
          let buffer: ArrayBuffer;

          if (image.data instanceof File || image.data instanceof Blob) {
            buffer = await blobToArrayBuffer(image.data);
          } else if (typeof image.data === 'string') {
            // Convert data URL to ArrayBuffer
            if (image.data.startsWith('data:')) {
              const response = await fetch(image.data);
              buffer = await response.arrayBuffer();
            } else {
              // Fetch URL
              const response = await this.fetchWithTimeout(image.data);
              buffer = await response.arrayBuffer();
            }
          } else {
            buffer = image.data;
          }

          return { data: buffer, format: image.format };
        })
      );

      // Use the image scaling service
      return imageScaling.scaleBatch(bufferImages, options);
    } catch (error) {
      console.error('Batch scaling error:', error);
      throw error;
    }
  }

  /**
   * Get optimal format for a specific use case
   * @param targetSize - Target file size in bytes
   * @param requiresTransparency - Whether transparency is required
   * @returns Recommended format and quality settings
   */
  public getOptimalImageFormat(
    targetSize: number,
    requiresTransparency: boolean
  ): { format: string; quality: number; optimizationLevel: number } {
    return imageScaling.getOptimalFormat(targetSize, requiresTransparency);
  }

  /**
   * Enhanced image conversion with advanced options
   * This method provides compatibility with the old enhancedFormatConversion API
   */
  @handleErrors(ErrorType.PROCESSING, 'Failed to optimize image conversion')
  public async optimizeConversion(
    imageData: ArrayBuffer | File | Blob | string,
    sourceFormat: string,
    options: EnhancedConversionOptions
  ): Promise<EnhancedConversionResult> {
    const startTime = Date.now();

    try {
      // Convert options to unified format
      const unifiedOptions = convertToUnifiedOptions(options);

      // Process the image
      const result = await this.processImage(
        imageData,
        unifiedOptions
      );

      // Create enhanced result
      const enhancedResult: EnhancedConversionResult = {
        buffer: result.buffer || new ArrayBuffer(0),
        format: result.format || options.format as string || sourceFormat,
        width: result.width,
        height: result.height,
        originalSize: imageData instanceof ArrayBuffer ? imageData.byteLength :
                     (imageData instanceof Blob ? imageData.size :
                     (typeof imageData === 'string' ? imageData.length : 0)),
        newSize: result.byteSize || 0,
        compressionRatio: 1,
        processingTime: result.processingTime,
        optimizationTechniquesApplied: ['unified-image-service'],
        metadataStatus: {
          preserved: [],
          removed: [],
          sizeReduction: 0
        },
        success: true,
        url: result.url
      };

      // Calculate compression ratio
      if (enhancedResult.originalSize > 0 && enhancedResult.newSize > 0) {
        enhancedResult.compressionRatio = enhancedResult.originalSize / enhancedResult.newSize;
      }

      // Add optimization techniques based on options
      if (options.analyzeContent) {
        enhancedResult.optimizationTechniquesApplied.push('content-analysis');
      }

      if (options.adaptiveCompression) {
        enhancedResult.optimizationTechniquesApplied.push('adaptive-compression');
      }

      if (options.format === 'auto') {
        enhancedResult.optimizationTechniquesApplied.push('format-optimization');
      }

      if (options.metadataStrategy === 'strip-all' || options.metadataStrategy === 'minimal') {
        enhancedResult.optimizationTechniquesApplied.push('metadata-optimization');
      }

      // Add format-specific optimizations
      if ((options.enhancedPngOptions && enhancedResult.format === 'png') ||
          (options.enhancedJpegOptions && (enhancedResult.format === 'jpeg' || enhancedResult.format === 'jpg')) ||
          (options.enhancedWebpOptions && enhancedResult.format === 'webp')) {
        enhancedResult.optimizationTechniquesApplied.push('format-specific-enhancements');
      }

      // Add content-specific optimizations
      if (options.detectText || options.detectGradients || options.detectPatterns) {
        enhancedResult.optimizationTechniquesApplied.push('content-specific-optimization');
      }

      // Estimate speed gain based on compression ratio
      if (enhancedResult.compressionRatio > 1) {
        // Rough estimate: 100KB at 3G is about 100ms, so we can estimate time saved
        const estimatedSizeSavingInKB = (enhancedResult.originalSize - enhancedResult.newSize) / 1024;
        enhancedResult.speedGain = estimatedSizeSavingInKB * 0.001; // seconds saved
      }

      return enhancedResult;
    } catch (error) {
      console.error('Enhanced conversion error:', error);

      // Return error result
      return {
        buffer: new ArrayBuffer(0),
        format: options.format as string || sourceFormat,
        width: 0,
        height: 0,
        originalSize: imageData instanceof ArrayBuffer ? imageData.byteLength :
                     (imageData instanceof Blob ? imageData.size :
                     (typeof imageData === 'string' ? imageData.length : 0)),
        newSize: 0,
        compressionRatio: 0,
        processingTime: Date.now() - startTime,
        optimizationTechniquesApplied: [],
        metadataStatus: {
          preserved: [],
          removed: [],
          sizeReduction: 0
        },
        success: false
      };
    }
  }

  /**
   * Batch optimize multiple images
   */
  @handleErrors(ErrorType.PROCESSING, 'Failed to optimize image batch')
  public async optimizeBatch(
    images: Array<{ data: ArrayBuffer | File | Blob | string; format: string; filename?: string }>,
    options: EnhancedConversionOptions
  ): Promise<EnhancedConversionResult[]> {
    try {
      // Process each image
      const results = await Promise.all(
        images.map(async (image) => {
          return this.optimizeConversion(image.data, image.format, options);
        })
      );

      return results;
    } catch (error) {
      console.error('Batch optimization error:', error);

      // Return error results
      return images.map(image => ({
        buffer: new ArrayBuffer(0),
        format: options.format as string || image.format,
        width: 0,
        height: 0,
        originalSize: image.data instanceof ArrayBuffer ? image.data.byteLength :
                     (image.data instanceof Blob ? image.data.size :
                     (typeof image.data === 'string' ? image.data.length : 0)),
        newSize: 0,
        compressionRatio: 0,
        processingTime: 0,
        optimizationTechniquesApplied: [],
        metadataStatus: {
          preserved: [],
          removed: [],
          sizeReduction: 0
        },
        success: false
      }));
    }
  }
}

// Create and export singleton instance
export const unifiedImageService = new UnifiedImageService();
export default unifiedImageService;