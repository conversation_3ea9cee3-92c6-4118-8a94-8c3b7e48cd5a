import { Rule, LogicalOperator, RuleType } from '@/types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Generate automatic rules - Disabled
 * @returns Empty array (automatic rules disabled)
 */
export const generateAutomaticRules = (): Rule[] => {
  // Automatic rule generation is disabled
  console.log('Automatic rule generation is disabled.');
  return [];
};

/**
 * Validate rule structure before saving
 * @param rule Rule to validate
 * @returns Validated rule
 */
export const validateRuleStructure = (rule: Rule): Rule => {
  // Early return for null or undefined rules
  if (!rule) {
    console.error('Invalid rule: Rule is null or undefined');
    return {
      id: uuidv4(),
      name: 'Unnamed Rule',
      description: '',
      type: 'IF_THEN',
      conditions: [],
      order: 1,
      priority: 'medium'
    };
  }
  
  // Create a deep copy to avoid mutating the original
  const validatedRule = JSON.parse(JSON.stringify(rule));
  
  // Ensure required properties exist
  if (!validatedRule.id) validatedRule.id = uuidv4();
  if (!validatedRule.name) validatedRule.name = 'Unnamed Rule';
  if (!validatedRule.type) validatedRule.type = 'IF_THEN';
  if (!validatedRule.order) validatedRule.order = 1;
  if (!validatedRule.description) validatedRule.description = '';
  
  // Initialize priority if not set (medium is default)
  if (!validatedRule.priority) validatedRule.priority = 'medium';
  
  // Convert priority to numeric order if needed
  validatedRule.order = getOrderFromPriority(validatedRule.priority, validatedRule.order);
  
  // Ensure conditions are properly structured
  if (!validatedRule.conditions || !Array.isArray(validatedRule.conditions)) {
    validatedRule.conditions = [];
  }
  
  // For GROUP rules, ensure we have valid groupLayers array
  if (validatedRule.type === 'GROUP') {
    if (!validatedRule.groupLayers || !Array.isArray(validatedRule.groupLayers)) {
      validatedRule.groupLayers = [];
    }
    
    // Ensure empty conditions array for GROUP rules
    validatedRule.conditions = [];
    
    console.log('Rule validation - GROUP rule:', validatedRule.name, 'with', validatedRule.groupLayers.length, 'layers');
    return validatedRule;
  }
  
  // For IF_THEN rules, ensure we have valid conditions
  if (validatedRule.type === 'IF_THEN') {
    // Separate IF and THEN conditions
    let ifConditions = validatedRule.conditions.filter(c => c.layerId && !c.targetLayerId);
    const thenConditions = validatedRule.conditions.filter(c => c.targetLayerId);
    
    console.log('Rule validation - IF conditions:', JSON.stringify(ifConditions));
    console.log('Rule validation - THEN conditions:', JSON.stringify(thenConditions));
    
    // Ensure we have at least one IF condition
    if (ifConditions.length === 0) {
      validatedRule.conditions.push({ layerId: null });
    }
    
    // Make sure all IF conditions after the first one have a logicalOperator
    // and that the operator is explicitly defined as 'AND' or 'OR'
    const validatedIfConditions = ifConditions.map((condition, index) => {
      if (index > 0) {
        // Get the current logical operator value
        const currentOp = condition.logicalOperator;
        console.log(`Validating condition #${index+1} with raw logicalOperator="${currentOp}"`);
        
        // Normalize to strings for comparison
        const opString = String(currentOp || '').trim().toUpperCase();
        
        // Force explicit value
        const normalizedOp = opString === 'OR' ? 'OR' : 'AND';
        console.log(`Normalized logicalOperator to "${normalizedOp}" for condition #${index+1}`);
        
        // Create a new object with the normalized value
        return {
          ...condition,
          logicalOperator: normalizedOp
        };
      }
      return condition;
    });
    
    // Replace the original ifConditions with the validated ones
    ifConditions = validatedIfConditions;
    
    // Ensure we have at least one THEN condition
    if (thenConditions.length === 0) {
      validatedRule.conditions.push({ 
        layerId: null, 
        targetLayerId: null, 
        operator: 'MUST_HAVE' 
      });
    }
    
    // Validate each THEN condition
    thenConditions.forEach(condition => {
      if (!condition.targetLayerId) condition.targetLayerId = null;
      if (!condition.operator) condition.operator = 'MUST_HAVE';
      
      // Special case: CANNOT_HAVE with no specific trait should use ALL_TRAITS
      if (condition.operator === 'CANNOT_HAVE' && (condition.targetTraitId === undefined || condition.targetTraitId === null || condition.targetTraitId === '')) {
        console.log('Rule validation - Fixing CANNOT_HAVE with no trait: setting to ALL_TRAITS');
        condition.targetTraitId = 'ALL_TRAITS';
      }
    });
    
    // Explicitly rebuild the conditions array to ensure correct order and structure
    let newConditions = [
      ...ifConditions.map((c, index) => ({
        ...c,
        // Only apply logicalOperator to conditions after the first one
        ...(index > 0 ? { logicalOperator: c.logicalOperator || 'AND' } : {})
      })),
      ...thenConditions
    ];
    
    // Replace the conditions array
    validatedRule.conditions = newConditions;
    
    console.log('Rule validation - Rebuilt conditions:', JSON.stringify(newConditions));
  }
  
  return validatedRule;
};

/**
 * Convert priority string to numeric order value
 * @param priority Priority string
 * @param defaultOrder Default order value
 * @returns Numeric order value
 */
export const getOrderFromPriority = (
  priority: 'highest' | 'high' | 'medium' | 'low' | 'lowest' | string, 
  defaultOrder: number = 1
): number => {
  switch (priority) {
    case 'highest': return 1;
    case 'high': return 10;
    case 'medium': return 50;
    case 'low': return 100;
    case 'lowest': return 500;
    default: return defaultOrder;
  }
};

/**
 * Convert numeric order to priority string
 * @param order Numeric order
 * @returns Priority string
 */
export const getPriorityFromOrder = (order: number): 'highest' | 'high' | 'medium' | 'low' | 'lowest' => {
  if (order <= 1) return 'highest';
  if (order <= 10) return 'high';
  if (order <= 50) return 'medium';
  if (order <= 100) return 'low';
  return 'lowest';
};

/**
 * Create a new rule group
 * @param name Group name
 * @param layerIds Array of layer IDs
 * @param priority Optional priority
 * @returns New rule group
 */
export const createRuleGroup = (
  name: string,
  layerIds: string[],
  priority: 'highest' | 'high' | 'medium' | 'low' | 'lowest' = 'medium'
): Rule => {
  return {
    id: uuidv4(),
    name,
    description: `Group rule for ${layerIds.length} layers`,
    type: 'GROUP',
    conditions: [], // GROUP rules don't use conditions
    groupLayers: layerIds,
    order: getOrderFromPriority(priority),
    priority
  };
};

/**
 * Create a new IF-THEN rule
 * @param name Rule name
 * @param ifConditions Array of IF conditions
 * @param thenConditions Array of THEN conditions
 * @param priority Optional priority
 * @returns New IF-THEN rule
 */
export const createIfThenRule = (
  name: string,
  ifConditions: any[],
  thenConditions: any[],
  priority: 'highest' | 'high' | 'medium' | 'low' | 'lowest' = 'medium'
): Rule => {
  // Prepare conditions with proper structure
  const formattedIfConditions = ifConditions.map((condition, index) => ({
    layerId: condition.layerId,
    traitId: condition.traitId,
    ...(index > 0 ? { logicalOperator: condition.logicalOperator || 'AND' } : {})
  }));
  
  const formattedThenConditions = thenConditions.map(condition => ({
    targetLayerId: condition.targetLayerId,
    targetTraitId: condition.targetTraitId,
    operator: condition.operator || 'MUST_HAVE'
  }));
  
  return {
    id: uuidv4(),
    name,
    description: '',
    type: 'IF_THEN',
    conditions: [
      ...formattedIfConditions,
      ...formattedThenConditions
    ],
    order: getOrderFromPriority(priority),
    priority
  };
};

/**
 * Load rules from local storage
 * @param storageKey Storage key
 * @returns Loaded rules
 */
export const loadRulesFromStorage = (storageKey: string): Rule[] => {
  try {
    const savedRules = localStorage.getItem(storageKey);
    if (savedRules) {
      try {
        const parsedRules = JSON.parse(savedRules);
        
        if (!Array.isArray(parsedRules)) {
          console.error('Invalid rules format in localStorage, expected array but got:', typeof parsedRules);
          return [];
        }
        
        // Validate each rule structure and filter out invalid ones
        const validRules = parsedRules
          .filter(rule => rule && typeof rule === 'object')
          .map(rule => validateRuleStructure(rule));
        
        console.log(`Loaded ${validRules.length} rules from storage`);
        return validRules;
      } catch (parseError) {
        console.error('Error parsing rules from localStorage:', parseError);
        return [];
      }
    }
  } catch (error) {
    console.error('Error accessing localStorage:', error);
  }
  return [];
};

/**
 * Save rules to local storage
 * @param rules Rules
 * @param storageKey Storage key
 */
export const saveRulesToStorage = (rules: Rule[], storageKey: string): void => {
  try {
    if (!Array.isArray(rules)) {
      console.error('Cannot save rules: rules is not an array', rules);
      return;
    }
    
    // Validate all rules before saving and filter out any null values
    const validatedRules = rules
      .filter(rule => rule && typeof rule === 'object')
      .map(rule => validateRuleStructure(rule));
    
    // Ensure all required properties exist on each rule
    const sanitizedRules = validatedRules.map(rule => {
      // Base properties for all rule types
      const baseRule = {
        id: rule.id,
        name: rule.name,
        description: rule.description,
        type: rule.type,
        conditions: rule.conditions,
        order: rule.order,
        priority: rule.priority || getPriorityFromOrder(rule.order)
      };
      
      // Add groupLayers for GROUP rules
      if (rule.type === 'GROUP' && rule.groupLayers) {
        return {
          ...baseRule,
          groupLayers: rule.groupLayers
        };
      }
      
      return baseRule;
    });
    
    localStorage.setItem(storageKey, JSON.stringify(sanitizedRules));
    console.log(`Saved ${sanitizedRules.length} rules to storage`);
  } catch (error) {
    console.error('Error saving rules to storage:', error);
    // Try to save again with a more basic approach if there's an error
    try {
      // Create completely new objects with only essential properties
      const basicRules = rules.map(rule => {
        const baseRule = {
          id: rule.id || uuidv4(),
          name: rule.name || 'Unnamed Rule',
          description: rule.description || '',
          type: rule.type || 'IF_THEN',
          conditions: [],
          order: rule.order || 1,
          priority: rule.priority || 'medium'
        };
        
        // Include groupLayers for GROUP rules
        if (rule.type === 'GROUP' && rule.groupLayers) {
          return {
            ...baseRule,
            groupLayers: rule.groupLayers
          };
        }
        
        return baseRule;
      });
      localStorage.setItem(storageKey, JSON.stringify(basicRules));
      console.log('Rules saved using fallback method');
    } catch (fallbackError) {
      console.error('Even fallback saving failed:', fallbackError);
    }
  }
};

/**
 * Sort rules by priority order
 * @param rules Array of rules to sort
 * @returns Sorted rules array
 */
export const sortRulesByPriority = (rules: Rule[]): Rule[] => {
  return [...rules].sort((a, b) => (a.order || 0) - (b.order || 0));
};



