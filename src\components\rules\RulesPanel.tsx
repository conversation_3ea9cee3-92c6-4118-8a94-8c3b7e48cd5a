import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>ton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  Divider,
  Tooltip,
  Switch,
  FormControlLabel,
  Badge,
  Paper,
  Stack
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Rule as RuleIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { Rule, RulePriority } from '@/types';
import { useRules } from '@/contexts/rules.context';
import { useNFT } from '@/contexts/NFTContext';
import { RulesDialog } from '@/components/dialogs/RulesDialog';

// Priority feature removed - rules are processed in order without priority levels

/**
 * Rules Panel Component
 * Displays and manages generation rules in a compact panel format
 */
const RulesPanel: React.FC = () => {
  const { rules, updateRules } = useRules();
  const { state } = useNFT();
  const { layers } = state;

  // Dialog state
  const [rulesDialogOpen, setRulesDialogOpen] = useState(false);

  // Stats
  const activeRulesCount = rules.filter(rule => rule.enabled).length;
  const totalRulesCount = rules.length;

  /**
   * Handle opening rules dialog
   */
  const handleOpenRulesDialog = () => {
    setRulesDialogOpen(true);
  };

  /**
   * Handle closing rules dialog
   */
  const handleCloseRulesDialog = () => {
    setRulesDialogOpen(false);
  };

  /**
   * Handle rules change from dialog
   */
  const handleRulesChange = (newRules: Rule[]) => {
    updateRules(newRules);
  };

  /**
   * Toggle rule enabled/disabled state
   */
  const handleToggleRule = (ruleId: string) => {
    const updatedRules = rules.map(rule =>
      rule.id === ruleId
        ? { ...rule, enabled: !rule.enabled }
        : rule
    );
    updateRules(updatedRules);
  };

  /**
   * Delete a rule
   */
  const handleDeleteRule = (ruleId: string) => {
    if (window.confirm('Are you sure you want to delete this rule?')) {
      const updatedRules = rules.filter(rule => rule.id !== ruleId);
      updateRules(updatedRules);
    }
  };

  /**
   * Get rule type display text
   */
  const getRuleTypeText = (rule: Rule): string => {
    switch (rule.type) {
      case 'IF_THEN':
        return 'Trait Rule';
      case 'LAYER_GROUP':
        return 'Layer Group';
      case 'TRAIT_GROUP':
        return 'Trait Group';
      default:
        return 'Unknown';
    }
  };

  /**
   * Get rule description for display
   */
  const getRuleDescription = (rule: Rule): string => {
    if (rule.description) {
      return rule.description;
    }

    // Generate description based on rule type
    switch (rule.type) {
      case 'IF_THEN':
        const ifConditions = rule.conditions?.filter(c => c.layerId && !c.targetLayerId) || [];
        const thenConditions = rule.conditions?.filter(c => c.targetLayerId) || [];
        
        if (ifConditions.length > 0 && thenConditions.length > 0) {
          return `IF ${ifConditions.length} condition(s) THEN ${thenConditions.length} action(s)`;
        }
        return 'Conditional rule';
      
      case 'LAYER_GROUP':
        const layerCount = rule.groupLayers?.length || 0;
        return `Groups ${layerCount} layer(s)`;
      
      case 'TRAIT_GROUP':
        const traitGroupCount = rule.groupTraits?.length || 0;
        return `Groups ${traitGroupCount} trait group(s)`;
      
      default:
        return 'Custom rule';
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Panel Header */}
      <Box className="panel-header">
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <RuleIcon sx={{ fontSize: '1.1rem', color: 'text.secondary' }} />
          <Typography variant="h6" sx={{ fontSize: '0.95rem', fontWeight: 600 }}>
            Rules
          </Typography>
          <Badge 
            badgeContent={activeRulesCount} 
            color="primary" 
            sx={{ ml: 0.5 }}
          >
            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
              ({totalRulesCount})
            </Typography>
          </Badge>
        </Box>
        
        <Tooltip title="Add new rule">
          <Button
            variant="outlined"
            size="small"
            startIcon={<AddIcon />}
            onClick={handleOpenRulesDialog}
            sx={{ 
              fontSize: '0.75rem',
              minWidth: 'auto',
              px: 1
            }}
          >
            Add
          </Button>
        </Tooltip>
      </Box>

      {/* Panel Content */}
      <Box className="panel-content">
        {rules.length === 0 ? (
          // Empty state
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            textAlign: 'center',
            p: 3
          }}>
            <RuleIcon sx={{ fontSize: '3rem', color: 'text.disabled', mb: 2 }} />
            <Typography variant="body1" color="text.secondary" gutterBottom>
              No rules defined
            </Typography>
            <Typography variant="body2" color="text.disabled" sx={{ mb: 2 }}>
              Create rules to control trait combinations and generation logic
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleOpenRulesDialog}
              size="small"
            >
              Create First Rule
            </Button>
          </Box>
        ) : (
          // Rules list
          <List sx={{ p: 0 }}>
            {rules.map((rule, index) => (
              <React.Fragment key={rule.id}>
                <ListItem
                  sx={{
                    px: 1,
                    py: 0.5,
                    opacity: rule.enabled ? 1 : 0.6,
                    '&:hover': {
                      backgroundColor: 'action.hover'
                    }
                  }}
                >
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {rule.name}
                        </Typography>
                        <Chip
                          label={getRuleTypeText(rule)}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.65rem', height: '18px' }}
                        />
                      </Box>
                    }
                    secondary={
                      <Typography variant="caption" color="text.secondary">
                        {getRuleDescription(rule)}
                      </Typography>
                    }
                  />
                  
                  <ListItemSecondaryAction>
                    <Stack direction="row" spacing={0.5} alignItems="center">
                      <Switch
                        edge="end"
                        checked={rule.enabled}
                        onChange={() => handleToggleRule(rule.id)}
                        size="small"
                      />
                      <Tooltip title="Edit rule">
                        <IconButton
                          edge="end"
                          size="small"
                          onClick={handleOpenRulesDialog}
                          sx={{ ml: 0.5 }}
                        >
                          <EditIcon sx={{ fontSize: '1rem' }} />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete rule">
                        <IconButton
                          edge="end"
                          size="small"
                          onClick={() => handleDeleteRule(rule.id)}
                          color="error"
                        >
                          <DeleteIcon sx={{ fontSize: '1rem' }} />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  </ListItemSecondaryAction>
                </ListItem>
                
                {index < rules.length - 1 && (
                  <Divider sx={{ mx: 1 }} />
                )}
              </React.Fragment>
            ))}
          </List>
        )}

        {/* Quick stats */}
        {rules.length > 0 && (
          <Box sx={{ p: 1, borderTop: '1px solid', borderColor: 'divider' }}>
            <Typography variant="caption" color="text.secondary">
              {activeRulesCount} of {totalRulesCount} rules active
            </Typography>
          </Box>
        )}
      </Box>

      {/* Rules Dialog */}
      <RulesDialog
        open={rulesDialogOpen}
        onClose={handleCloseRulesDialog}
        rules={rules}
        onRulesChange={handleRulesChange}
      />
    </Box>
  );
};

export default RulesPanel;
