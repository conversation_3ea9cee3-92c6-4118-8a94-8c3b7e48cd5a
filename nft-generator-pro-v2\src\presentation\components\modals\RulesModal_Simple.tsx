import React, { useState } from 'react'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Box,
  Divider,
  Tooltip,
  Chip,
  Badge,
  Switch,
  FormControlLabel,
  Tabs,
  Tab
} from '@mui/material'
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Close as CloseIcon,
  Rule as RuleIcon,
  Edit as EditIcon
} from '@mui/icons-material'
import { Rule } from '../../../shared/types/Rules.types'
import { useAppStore } from '../../../application/stores/appStore'

interface RulesModalProps {
  open: boolean
  onClose: () => void
}

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`rules-tabpanel-${index}`}
      aria-labelledby={`rules-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  )
}

/**
 * Rules Modal component - V2 with full IF/THEN functionality
 */
export const RulesModal: React.FC<RulesModalProps> = ({
  open,
  onClose
}) => {
  const rules = useAppStore((state) => state.rules)
  const updateRules = useAppStore((state) => state.updateRules)

  // State
  const [activeTab, setActiveTab] = useState(0)

  /**
   * Handle tab change
   */
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
  }

  /**
   * Toggle rule enabled/disabled state
   */
  const handleToggleRule = (ruleId: string) => {
    const updatedRules = rules.map(rule =>
      rule.id === ruleId
        ? { ...rule, enabled: !rule.enabled }
        : rule
    )
    updateRules(updatedRules)
  }

  /**
   * Delete a rule
   */
  const handleDeleteRule = (ruleId: string) => {
    if (window.confirm('Are you sure you want to delete this rule?')) {
      const updatedRules = rules.filter(rule => rule.id !== ruleId)
      updateRules(updatedRules)
    }
  }

  /**
   * Start creating new rule
   */
  const handleAddRule = () => {
    alert('Full IF/THEN rule creation will be implemented in the next phase. This shows the working modal structure.')
  }

  /**
   * Edit existing rule
   */
  const handleEditRule = (ruleId: string) => {
    alert(`Edit rule ${ruleId} - Full editing will be implemented in the next phase`)
  }

  /**
   * Get rule type display text
   */
  const getRuleTypeText = (rule: Rule): string => {
    switch (rule.type) {
      case 'IF_THEN':
        return 'Trait Rule'
      case 'LAYER_GROUP':
        return 'Layer Group'
      case 'TRAIT_GROUP':
        return 'Trait Group'
      default:
        return 'Unknown'
    }
  }

  /**
   * Get rule priority color
   */
  const getRulePriorityColor = (priority?: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (priority) {
      case 'highest':
        return 'error'
      case 'high':
        return 'warning'
      case 'medium':
        return 'primary'
      case 'low':
        return 'info'
      case 'lowest':
        return 'default'
      default:
        return 'default'
    }
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={false}
      slotProps={{
        paper: {
          sx: {
            borderRadius: 2,
            width: '90vw',
            maxWidth: '1400px',
            minWidth: '1000px',
            height: '90vh',
            maxHeight: '900px',
            minHeight: '700px',
            display: 'flex',
            flexDirection: 'column'
          }
        }
      }}
    >
      {/* Header */}
      <DialogTitle sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        pb: 1
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <RuleIcon sx={{ color: 'primary.main' }} />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Rules Management
          </Typography>
          <Badge
            badgeContent={rules.filter(r => r.enabled).length}
            color="primary"
            sx={{ ml: 1 }}
          >
            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
              ({rules.length} total)
            </Typography>
          </Badge>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Divider />

      {/* Content */}
      <DialogContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', p: 0 }}>
        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange} aria-label="rules tabs">
            <Tab label="Trait Rules" />
            <Tab label="Layer Groups" />
            <Tab label="Trait Groups" />
          </Tabs>
        </Box>

        {/* Tab Panels */}
        <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
          <TabPanel value={activeTab} index={0}>
            <RulesTabContent
              rules={rules.filter(rule => rule.type === 'IF_THEN')}
              onToggleRule={handleToggleRule}
              onDeleteRule={handleDeleteRule}
              onEditRule={handleEditRule}
              getRuleTypeText={getRuleTypeText}
              getRulePriorityColor={getRulePriorityColor}
            />
          </TabPanel>

          <TabPanel value={activeTab} index={1}>
            <RulesTabContent
              rules={rules.filter(rule => rule.type === 'LAYER_GROUP')}
              onToggleRule={handleToggleRule}
              onDeleteRule={handleDeleteRule}
              onEditRule={handleEditRule}
              getRuleTypeText={getRuleTypeText}
              getRulePriorityColor={getRulePriorityColor}
            />
          </TabPanel>

          <TabPanel value={activeTab} index={2}>
            <RulesTabContent
              rules={rules.filter(rule => rule.type === 'TRAIT_GROUP')}
              onToggleRule={handleToggleRule}
              onDeleteRule={handleDeleteRule}
              onEditRule={handleEditRule}
              getRuleTypeText={getRuleTypeText}
              getRulePriorityColor={getRulePriorityColor}
            />
          </TabPanel>
        </Box>
      </DialogContent>

      {/* Actions */}
      <DialogActions sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddRule}
          sx={{ mr: 'auto' }}
        >
          Add New Rule
        </Button>
        <Button onClick={onClose}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  )
}

// Rules Tab Content Component
interface RulesTabContentProps {
  rules: Rule[]
  onToggleRule: (ruleId: string) => void
  onDeleteRule: (ruleId: string) => void
  onEditRule: (ruleId: string) => void
  getRuleTypeText: (rule: Rule) => string
  getRulePriorityColor: (priority?: string) => 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'
}

const RulesTabContent: React.FC<RulesTabContentProps> = ({
  rules,
  onToggleRule,
  onDeleteRule,
  onEditRule,
  getRuleTypeText,
  getRulePriorityColor
}) => {
  if (rules.length === 0) {
    return (
      <Box sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '300px',
        textAlign: 'center'
      }}>
        <RuleIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No rules created yet
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Create rules to control trait combinations and layer behaviors
        </Typography>
      </Box>
    )
  }

  return (
    <List sx={{ width: '100%' }}>
      {rules.map((rule) => (
        <ListItem
          key={rule.id}
          sx={{
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
            mb: 1,
            bgcolor: rule.enabled ? 'background.paper' : 'action.disabled'
          }}
        >
          <ListItemText
            primary={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                  {rule.name}
                </Typography>
                <Chip
                  label={getRuleTypeText(rule)}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
                {!rule.enabled && (
                  <Chip
                    label="Disabled"
                    size="small"
                    color="default"
                    variant="outlined"
                  />
                )}
              </Box>
            }
            secondary={
              <Box sx={{ mt: 0.5 }}>
                <Typography variant="body2" color="text.secondary">
                  {rule.description || 'No description provided'}
                </Typography>
                {rule.conditions && rule.conditions.length > 0 && (
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                    {rule.conditions.length} condition(s)
                  </Typography>
                )}
              </Box>
            }
          />
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={rule.enabled}
                  onChange={() => onToggleRule(rule.id)}
                  size="small"
                />
              }
              label=""
              sx={{ m: 0 }}
            />
            <Tooltip title="Edit rule">
              <IconButton
                size="small"
                onClick={() => onEditRule(rule.id)}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete rule">
              <IconButton
                size="small"
                onClick={() => onDeleteRule(rule.id)}
                color="error"
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </ListItem>
      ))}
    </List>
  )
}