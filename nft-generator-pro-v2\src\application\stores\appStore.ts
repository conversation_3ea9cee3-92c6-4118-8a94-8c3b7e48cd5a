import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { Project } from '../../domain/entities/Project'
import { Layer } from '../../domain/entities/Layer'
import { UIState, AppError, ProgressInfo, Trait } from '../../shared/types/Project.types'
import { layerImportService, ImportProgress, ImportResult } from '../../infrastructure/services/LayerImportService'
import { Rule } from '../../shared/types/Rules.types'

// No sample data - start with empty project
const createSampleRules = (): Rule[] => {
  return []
}

interface AppStore {
  // State
  currentProject: Project | null
  projects: Project[]
  uiState: UIState
  errors: AppError[]
  isLoading: boolean
  progress: ProgressInfo | null
  rules: Rule[]

  // Project Actions
  createProject: (name: string, description?: string) => void
  loadProject: (projectId: string) => void
  saveProject: () => Promise<void>
  updateProject: (project: Project) => void
  deleteProject: (projectId: string) => void
  updateProjectSettings: (settings: Partial<Project['settings']>) => void

  // Layer Actions
  addLayer: (layer: Layer) => void
  removeLayer: (layerId: string) => void
  updateLayer: (layerId: string, updates: Partial<Layer>) => void
  reorderLayers: (layerIds: string[]) => void
  importLayers: (options?: { replaceExisting?: boolean }) => Promise<ImportResult>
  toggleLayerVisibility: (layerId: string) => void
  toggleLayerLock: (layerId: string) => void
  setLayerRarityConstraint: (layerId: string, maxRarity: number, isLocked: boolean) => void
  deleteLayer: (layerId: string) => void
  duplicateLayer: (layerId: string) => void
  renameLayer: (layerId: string, newName: string) => void
  clearAllLayers: () => void

  // UI Actions
  setSelectedLayer: (layerId: string | undefined) => void
  setSelectedTraits: (traitIds: string[]) => void
  setSelectedTraitGroup: (groupId: string | undefined) => void
  toggleTraitSelection: (traitId: string) => void
  setViewMode: (mode: UIState['viewMode']) => void
  setFilterText: (text: string) => void
  setSortBy: (sortBy: UIState['sortBy'], direction?: UIState['sortDirection']) => void
  toggleLayerExpanded: (layerId: string) => void
  setShowHidden: (show: boolean) => void

  // Error Actions
  addError: (error: AppError) => void
  removeError: (errorId: string) => void
  clearErrors: () => void

  // Progress Actions
  setProgress: (progress: ProgressInfo | null) => void
  updateProgress: (current: number, message?: string) => void

  // Loading Actions
  setLoading: (loading: boolean) => void

  // Rules Actions
  addRule: (rule: Rule) => void
  updateRule: (ruleId: string, updates: Partial<Rule>) => void
  updateRules: (rules: Rule[]) => void
  deleteRule: (ruleId: string) => void
  toggleRuleEnabled: (ruleId: string) => void
  reorderRules: (ruleIds: string[]) => void

  // Utility Actions
  reset: () => void
}

const initialUIState: UIState = {
  selectedLayerId: undefined,
  selectedTraitIds: [],
  selectedTraitGroup: undefined,
  viewMode: 'grid',
  filterText: '',
  sortBy: 'order',
  sortDirection: 'asc',
  showHidden: false,
  expandedLayers: []
}

export const useAppStore = create<AppStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial State
        currentProject: null,
        projects: [],
        uiState: initialUIState,
        errors: [],
        isLoading: false,
        progress: null,
        rules: [],

        // Project Actions
        createProject: (name: string, description?: string) => {
          const project = new Project({ name, description })

          // Start with empty project - no sample data
          set((state) => ({
            currentProject: project,
            projects: [...state.projects, project],
            rules: [],
            uiState: { ...initialUIState }
          }))
        },

        loadProject: (projectId: string) => {
          const { projects } = get()
          const project = projects.find(p => p.id === projectId)
          if (project) {
            set({
              currentProject: project,
              uiState: { ...initialUIState }
            })
          } else {
            get().addError({
              code: 'PROJECT_NOT_FOUND',
              message: `Project with ID ${projectId} not found`,
              timestamp: new Date(),
              context: 'loadProject'
            })
          }
        },

        saveProject: async () => {
          const { currentProject } = get()
          if (!currentProject) return

          set({ isLoading: true })

          try {
            // Here you would typically save to a backend or local storage
            // For now, we'll just update the projects array
            set((state) => ({
              projects: state.projects.map(p =>
                p.id === currentProject.id ? currentProject : p
              ),
              isLoading: false
            }))
          } catch (error) {
            get().addError({
              code: 'SAVE_FAILED',
              message: 'Failed to save project',
              details: error,
              timestamp: new Date(),
              context: 'saveProject'
            })
            set({ isLoading: false })
          }
        },

        updateProject: (updatedProject: Project) => {
          set((state) => ({
            currentProject: updatedProject,
            projects: state.projects.map(p =>
              p.id === updatedProject.id ? updatedProject : p
            )
          }))

          // Force persistence by triggering Zustand persist
          console.log('Project updated and persisted:', updatedProject.id)
        },

        deleteProject: (projectId: string) => {
          set((state) => {
            const newProjects = state.projects.filter(p => p.id !== projectId)
            const newCurrentProject = state.currentProject?.id === projectId
              ? null
              : state.currentProject

            return {
              projects: newProjects,
              currentProject: newCurrentProject,
              uiState: newCurrentProject ? state.uiState : initialUIState
            }
          })
        },

        updateProjectSettings: (settings) => {
          const { currentProject } = get()
          if (!currentProject) return

          currentProject.updateSettings(settings)
          set({ currentProject })
        },

        // Layer Actions
        addLayer: (layer: Layer) => {
          const { currentProject } = get()
          if (!currentProject) return

          currentProject.addLayer(layer)
          set({ currentProject })
        },

        removeLayer: (layerId: string) => {
          const { currentProject } = get()
          if (!currentProject) return

          currentProject.removeLayer(layerId)

          // Clear selection if removed layer was selected
          set((state) => ({
            currentProject,
            uiState: {
              ...state.uiState,
              selectedLayerId: state.uiState.selectedLayerId === layerId
                ? undefined
                : state.uiState.selectedLayerId,
              expandedLayers: state.uiState.expandedLayers.filter(id => id !== layerId)
            }
          }))
        },

        updateLayer: (layerId: string, updates: Partial<Layer>) => {
          set((state) => {
            if (!state.currentProject) return state

            // Create new project instance with updated layer
            const updatedProject = new Project(state.currentProject)
            const layerIndex = updatedProject.layers.findIndex(l => l.id === layerId)
            if (layerIndex !== -1) {
              updatedProject.layers[layerIndex] = { ...updatedProject.layers[layerIndex], ...updates }
              updatedProject.touch()
            }

            return { currentProject: updatedProject }
          })
        },

        reorderLayers: (layerIds: string[]) => {
          set((state) => {
            if (!state.currentProject) return state

            // Create new project instance with reordered layers
            const updatedProject = new Project(state.currentProject)
            updatedProject.layers = updatedProject.layers.map(layer => {
              const newIndex = layerIds.indexOf(layer.id)
              if (newIndex !== -1) {
                // Deep copy layer to preserve all properties including traitGroups
                const reorderedLayer = {
                  ...layer,
                  order: newIndex,
                  // Explicitly preserve traitGroups if it exists
                  traitGroups: layer.traitGroups || [],
                  // Preserve hasTraitGroups method if it exists
                  hasTraitGroups: layer.hasTraitGroups
                }

                return reorderedLayer
              }
              return layer
            })

            // Persist layer order to storage
            console.log('Saving layer order:', layerIds);

            // Force persistence by calling updateProject
            setTimeout(() => {
              get().updateProject(updatedProject);
            }, 0);

            return { currentProject: updatedProject }
          })
        },

        importLayers: async (options = {}) => {
          const { currentProject } = get()
          if (!currentProject) {
            return {
              success: false,
              layers: [],
              errors: ['No project selected'],
              warnings: []
            }
          }

          try {
            set({ isLoading: true })

            // Clear existing layers if replace mode
            if (options.replaceExisting) {
              currentProject.layers = []
            }

            const result = await layerImportService.importFromDirectory({
              onProgress: (progress: ImportProgress) => {
                set({
                  progress: {
                    stage: progress.stage,
                    current: progress.current,
                    total: progress.total,
                    message: progress.message,
                    percentage: Math.round((progress.current / progress.total) * 100)
                  }
                })
              }
            })

            if (result.success) {
              // Add imported layers to project
              result.layers.forEach(layer => {
                currentProject.addLayer(layer)
              })

              // Force store update to reflect trait rarity changes
              const updatedProject = new Project(currentProject)

              set({
                currentProject: updatedProject,
                progress: null,
                isLoading: false
              })

              // Additional force update after a short delay to ensure UI refresh
              setTimeout(() => {
                set((state) => ({
                  ...state,
                  currentProject: new Project(state.currentProject!)
                }))


              }, 100)
            } else {
              set({
                errors: result.errors.map(error => ({
                  code: 'IMPORT_ERROR',
                  message: error,
                  timestamp: new Date(),
                  context: 'importLayers'
                })),
                progress: null,
                isLoading: false
              })
            }

            return result
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Import failed'
            set({
              errors: [{
                code: 'IMPORT_FAILED',
                message: errorMessage,
                timestamp: new Date(),
                context: 'importLayers'
              }],
              progress: null,
              isLoading: false
            })

            return {
              success: false,
              layers: [],
              errors: [errorMessage],
              warnings: []
            }
          }
        },

        toggleLayerVisibility: (layerId: string) => {
          set((state) => {
            if (!state.currentProject) return state

            // V1-style simple immutable update (more reliable with Zustand)
            const updatedProject = {
              ...state.currentProject,
              layers: state.currentProject.layers.map(layer =>
                layer.id === layerId
                  ? { ...layer, isVisible: !layer.isVisible }
                  : layer
              ),
              updatedAt: new Date() // Update timestamp
            }

            console.log(`🔍 Toggling layer visibility: ${layerId}`)
            console.log(`  Layer found:`, updatedProject.layers.find(l => l.id === layerId))

            return { currentProject: updatedProject }
          })
        },

        toggleLayerLock: (layerId: string) => {
          set((state) => {
            if (!state.currentProject) return state

            // V1-style simple immutable update (more reliable with Zustand)
            const updatedProject = {
              ...state.currentProject,
              layers: state.currentProject.layers.map(layer =>
                layer.id === layerId
                  ? { ...layer, isLocked: !layer.isLocked }
                  : layer
              ),
              updatedAt: new Date() // Update timestamp
            }

            return { currentProject: updatedProject }
          })
        },

        setLayerRarityConstraint: (layerId: string, maxRarity: number, isLocked: boolean = true) => {
          set((state) => {
            if (!state.currentProject) return state

            const updatedProject = {
              ...state.currentProject,
              layers: state.currentProject.layers.map(layer =>
                layer.id === layerId
                  ? {
                      ...layer,
                      rarityConstraint: {
                        maxRarity: Math.max(0, Math.min(100, maxRarity)),
                        isLocked
                      }
                    }
                  : layer
              ),
              updatedAt: new Date()
            }



            return { currentProject: updatedProject }
          })
        },

        deleteLayer: (layerId: string) => {
          const { currentProject } = get()
          if (!currentProject) return

          currentProject.removeLayer(layerId)

          // Clear selection if deleted layer was selected
          set((state) => ({
            currentProject,
            uiState: {
              ...state.uiState,
              selectedLayerId: state.uiState.selectedLayerId === layerId
                ? undefined
                : state.uiState.selectedLayerId,
              expandedLayers: state.uiState.expandedLayers.filter(id => id !== layerId)
            }
          }))
        },

        duplicateLayer: (layerId: string) => {
          const { currentProject } = get()
          if (!currentProject) return

          const layer = currentProject.layers.find(l => l.id === layerId)
          if (layer) {
            // Create a deep copy of the layer
            const duplicatedLayer = new Layer({
              ...(layer as any).toJSON(),
              id: undefined, // Will generate new ID
              name: `${layer.name} Copy`,
              order: layer.order + 0.5 // Insert after original
            })

            currentProject.addLayer(duplicatedLayer)
            set({ currentProject })
          }
        },

        renameLayer: (layerId: string, newName: string) => {
          set((state) => {
            if (!state.currentProject) return state

            // Create new project instance with renamed layer
            const updatedProject = new Project(state.currentProject)
            const layerIndex = updatedProject.layers.findIndex(l => l.id === layerId)
            if (layerIndex !== -1) {
              updatedProject.layers[layerIndex] = {
                ...updatedProject.layers[layerIndex],
                name: newName.trim()
              }
              updatedProject.touch()
            }

            return { currentProject: updatedProject }
          })
        },

        clearAllLayers: () => {
          const { currentProject } = get()
          if (!currentProject) return

          // Clear all layers
          currentProject.layers = []

          // Reset UI state
          set((state) => ({
            currentProject,
            uiState: {
              ...state.uiState,
              selectedLayerId: undefined,
              selectedTraitIds: [],
              expandedLayers: []
            }
          }))
        },

        // UI Actions
        setSelectedLayer: (layerId) => {
          set((state) => ({
            uiState: { ...state.uiState, selectedLayerId: layerId }
          }))
        },

        setSelectedTraits: (traitIds) => {
          set((state) => ({
            uiState: { ...state.uiState, selectedTraitIds: traitIds }
          }))
        },

        setSelectedTraitGroup: (groupId) => {
          set((state) => ({
            uiState: { ...state.uiState, selectedTraitGroup: groupId }
          }))
        },

        toggleTraitSelection: (traitId) => {
          set((state) => {
            const { selectedTraitIds } = state.uiState
            const newSelection = selectedTraitIds.includes(traitId)
              ? selectedTraitIds.filter(id => id !== traitId)
              : [...selectedTraitIds, traitId]

            return {
              uiState: { ...state.uiState, selectedTraitIds: newSelection }
            }
          })
        },

        setViewMode: (mode) => {
          set((state) => ({
            uiState: { ...state.uiState, viewMode: mode }
          }))
        },

        setFilterText: (text) => {
          set((state) => ({
            uiState: { ...state.uiState, filterText: text }
          }))
        },

        setSortBy: (sortBy, direction = 'asc') => {
          set((state) => ({
            uiState: { ...state.uiState, sortBy, sortDirection: direction }
          }))
        },

        toggleLayerExpanded: (layerId) => {
          set((state) => {
            const { expandedLayers } = state.uiState
            const newExpanded = expandedLayers.includes(layerId)
              ? expandedLayers.filter(id => id !== layerId)
              : [...expandedLayers, layerId]

            return {
              uiState: { ...state.uiState, expandedLayers: newExpanded }
            }
          })
        },

        setShowHidden: (show) => {
          set((state) => ({
            uiState: { ...state.uiState, showHidden: show }
          }))
        },

        // Error Actions
        addError: (error) => {
          set((state) => ({
            errors: [...state.errors, { ...error, id: Date.now().toString() } as AppError & { id: string }]
          }))
        },

        removeError: (errorId) => {
          set((state) => ({
            errors: state.errors.filter((error: any) => error.id !== errorId)
          }))
        },

        clearErrors: () => {
          set({ errors: [] })
        },

        // Progress Actions
        setProgress: (progress) => {
          set({ progress })
        },

        updateProgress: (current, message) => {
          set((state) => {
            if (!state.progress) return state

            return {
              progress: {
                ...state.progress,
                current,
                percentage: (current / state.progress.total) * 100,
                message: message || state.progress.message
              }
            }
          })
        },

        // Loading Actions
        setLoading: (loading) => {
          set({ isLoading: loading })
        },



        // Rules Actions
        addRule: (rule: Rule) => {
          set((state) => ({
            rules: [...state.rules, rule]
          }))
        },

        updateRule: (ruleId: string, updates: Partial<Rule>) => {
          set((state) => ({
            rules: state.rules.map(rule =>
              rule.id === ruleId ? { ...rule, ...updates } : rule
            )
          }))
        },

        updateRules: (rules: Rule[]) => {
          set({ rules })
        },

        deleteRule: (ruleId: string) => {
          set((state) => ({
            rules: state.rules.filter(rule => rule.id !== ruleId)
          }))
        },

        toggleRuleEnabled: (ruleId: string) => {
          set((state) => ({
            rules: state.rules.map(rule =>
              rule.id === ruleId ? { ...rule, enabled: !rule.enabled } : rule
            )
          }))
        },

        reorderRules: (ruleIds: string[]) => {
          set((state) => ({
            rules: ruleIds.map((id, index) => {
              const rule = state.rules.find(r => r.id === id)
              return rule ? { ...rule, order: index + 1 } : rule
            }).filter(Boolean) as Rule[]
          }))
        },

        // Utility Actions
        reset: () => {
          set({
            currentProject: null,
            uiState: initialUIState,
            errors: [],
            isLoading: false,
            progress: null,
            rules: []
          })
        }
      }),
      {
        name: 'nft-generator-app-store',
        partialize: (state) => ({
          projects: state.projects,
          currentProject: state.currentProject,
          rules: state.rules
        }),
        onRehydrateStorage: () => (state) => {
          if (state) {
            // Convert plain objects back to Project instances
            state.projects = state.projects.map(p =>
              p instanceof Project ? p : Project.fromJSON(p as any)
            )

            if (state.currentProject && !(state.currentProject instanceof Project)) {
              state.currentProject = Project.fromJSON(state.currentProject as any)
            }
          }
        }
      }
    ),
    { name: 'NFT Generator App Store' }
  )
)
