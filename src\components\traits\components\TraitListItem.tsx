import React from 'react';
import { Box, ListItem, Typography, Slider } from '@mui/material';
import { alpha, useTheme } from '@mui/material/styles';
import { TraitRarityLockControl } from '@/components/rarity';
import { EditableRarityValue } from './';
import { Trait } from '@/types';

interface TraitListItemProps {
  trait: Trait;
  isSelected: boolean;
  layerId: string;
  onSelect: () => void;
  onRarityChange: (value: number) => void;
  onRarityChangeCommitted: (value: number) => void;
}

const TraitListItem: React.FC<TraitListItemProps> = ({
  trait,
  isSelected,
  layerId,
  onSelect,
  onRarityChange,
  onRarityChangeCommitted
}) => {
  const theme = useTheme();
  
  return (
    <ListItem 
      disablePadding
      onClick={onSelect}
      sx={{ 
        mb: 1, 
        border: isSelected 
          ? `1px solid ${theme.palette.primary.main}` 
          : '1px solid rgba(0,0,0,0.12)',
        borderRadius: 1,
        overflow: 'hidden',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        '&:hover': {
          bgcolor: 'action.hover',
          boxShadow: 1
        },
        bgcolor: isSelected ? alpha(theme.palette.primary.main, 0.08) : 'transparent',
        '& .MuiBox-root': {
          overflow: 'hidden'
        }
      }}
    >
      <Box sx={{ width: '100%', p: 1, overflow: 'hidden' }}>
        {/* Trait name and thumbnail */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, overflow: 'hidden' }}>
          <Box 
            sx={{ 
              flexShrink: 0, 
              width: 60, 
              height: 60, 
              mr: 2, 
              overflow: 'hidden',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'divider',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: 'background.paper'
            }}
          >
            <img
              src={trait.imageUrl || ''}
              alt={trait.name}
              style={{ maxWidth: '90%', maxHeight: '90%', objectFit: 'contain' }}
              onError={(e) => {
                console.log('Image load error for trait:', trait.name, 'URL:', trait.imageUrl);
                // Try to reload from persistent storage
                if (trait.imageUrl && trait.imageUrl.startsWith('blob:')) {
                  // Blob URL might be invalid, try to regenerate
                  import('@/services/memory/image-persistence.service').then(({ getTraitImage }) => {
                    getTraitImage(trait.layerId || 'unknown', trait.name).then(newUrl => {
                      if (newUrl && e.target) {
                        (e.target as HTMLImageElement).src = newUrl;
                      }
                    });
                  });
                }
              }}
            />
          </Box>
          <Box sx={{ flexGrow: 1 }}>
            <Typography 
              variant="body1" 
              sx={{ 
                fontWeight: isSelected ? 'bold' : 'normal',
                color: isSelected ? theme.palette.primary.main : 'inherit'
              }}
            >
              {trait.name}
            </Typography>
          </Box>
        </Box>
        
        {/* Rarity controls */}
        <Box 
          sx={{ display: 'flex', flexDirection: 'column', mt: 1 }}
          onClick={(e) => e.stopPropagation()}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', width: 60 }}>
              <Typography variant="caption" sx={{ color: 'text.secondary', mr: 0.5 }}>
                Rarity:
              </Typography>
              <TraitRarityLockControl 
                traitId={trait.id}
                layerId={layerId}
                isLocked={trait.rarityLocked}
                size="small"
              />
            </Box>
            <Slider
              size="small"
              value={trait.rarity}
              onChange={(_, value) => onRarityChange(Number(value.toFixed(2)))}
              onChangeCommitted={(_, value) => onRarityChangeCommitted(Number(value.toFixed(2)))}
              valueLabelDisplay="auto"
              valueLabelFormat={(value) => `${value.toFixed(2)}%`}
              step={0.1}
              min={0.1}
              max={100}
              disabled={trait.rarityLocked}
              sx={{ flexGrow: 1, mx: 1 }}
            />
            {/* Editable rarity value */}
            <EditableRarityValue 
              rarity={trait.rarity}
              isLocked={trait.rarityLocked}
              isSelected={isSelected}
              onChange={onRarityChangeCommitted}
            />
          </Box>
        </Box>
      </Box>
    </ListItem>
  );
};

export default React.memo(TraitListItem);