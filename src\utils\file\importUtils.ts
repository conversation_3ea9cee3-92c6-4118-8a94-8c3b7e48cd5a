import { v4 as uuidv4 } from 'uuid';
import { Layer, Trait } from '@/types';
import { validateImageFile } from '@/utils/validation/fileValidation';
import { distributeRarityEvenly, RarityDistributionType, importTraitsWithRarity } from '@/utils/rarityUtils';
import { storeTraitImage } from '@/services/memory/image-persistence.service';

/**
 * Generate stable trait ID based on layer name and trait path
 * This ensures same traits get same IDs across imports
 */
export function generateStableTraitId(layerName: string, traitPath: string): string {
  // Create a stable hash from layer name and trait path
  const input = `${layerName}:${traitPath}`.toLowerCase();
  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  // Convert to positive hex string and pad
  const hexHash = Math.abs(hash).toString(16).padStart(8, '0');
  return `trait-${hexHash}`;
}

/**
 * Generate stable layer ID based on layer name
 */
export function generateStableLayerId(layerName: string): string {
  const input = layerName.toLowerCase();
  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }

  const hexHash = Math.abs(hash).toString(16).padStart(8, '0');
  return `layer-${hexHash}`;
}

/**
 * Process file paths into a hierarchical structure
 * @param filePaths Array of file paths
 * @returns Object with project name, path and hierarchical structure
 */
export const processFilePaths = (filePaths: string[]) => {
  // Extract base directory from first file path
  const baseDirMatch = filePaths[0].match(/^([^/\\]+)/);
  const baseDir = baseDirMatch ? baseDirMatch[1] : 'Unknown Project';

  // Group files by directory structure
  const layers: { [key: string]: { name: string, order: number, files: { path: string, name: string, subfolderPath?: string }[] } } = {};

  filePaths.forEach(path => {
    // Skip non-image files and hidden files
    if (!validateImageFile(path)) return;

    // Parse path components
    const pathParts = path.split(/[/\\]/);

    // Skip if path doesn't have enough parts (should at least have project/layer/file)
    if (pathParts.length < 3) return;

    // Determine layer name (directory after project name)
    const layerName = pathParts[1];

    // Get file name without extension
    const fileName = pathParts[pathParts.length - 1].replace(/\.\w+$/, '');

    // Initialize layer if not exists
    if (!layers[layerName]) {
      // Extract order from layer name if it follows the pattern "1-Background"
      const orderMatch = layerName.match(/^(\d+)-(.+)$/);
      const order = orderMatch ? parseInt(orderMatch[1], 10) : Object.keys(layers).length + 1;
      const cleanName = orderMatch ? orderMatch[2] : layerName;

      layers[layerName] = {
        name: cleanName,
        order: order,
        files: []
      };
    }

    // Determine subfolder path (if any)
    let subfolderPath = '';
    if (pathParts.length > 3) {
      // Get all directories between layer and file name
      subfolderPath = pathParts.slice(2, pathParts.length - 1).join('/');
    }

    // Add file to layer
    layers[layerName].files.push({
      path: path,
      name: fileName,
      subfolderPath: subfolderPath || undefined
    });
  });

  return {
    projectName: baseDir,
    projectPath: baseDir,
    layers
  };
};

/**
 * Process uploaded files from file input
 * @param files FileList from file input
 * @returns Object with layers, project name, and project path
 */
export const processUploadedFiles = async (files: FileList): Promise<{
  layers: Layer[],
  projectName: string,
  projectPath: string
}> => {
  try {
    // Convert FileList to array of file paths
    const filePaths: string[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      filePaths.push(file.webkitRelativePath || file.name);
    }

    // Process file paths into hierarchical structure
    const { projectName, projectPath, layers: layersObj } = processFilePaths(filePaths);

    // Convert to Layer and Trait arrays
    const layers: Layer[] = await Promise.all(
      Object.entries(layersObj).map(async ([_, layer]) => {
        // First, create traits without rarity
        const rawTraits = await Promise.all(
          layer.files.map(async file => {
            // Generate stable ID for trait
            const stableId = generateStableTraitId(layer.name, file.path);

            // Find actual file for image processing
            const actualFile = Array.from(files).find(f =>
              (f.webkitRelativePath || f.name) === file.path
            );

            let imageUrl = '';
            if (actualFile) {
              try {
                // Store image persistently and get stable URL
                imageUrl = await storeTraitImage(actualFile, layer.name, file.name);
              } catch (error) {
                console.error(`Failed to store image for ${file.name}:`, error);
                // Fallback to blob URL for immediate display
                imageUrl = URL.createObjectURL(actualFile);
              }
            }

            return {
              id: stableId,
              name: file.name,
              path: file.path,
              rarity: 1, // Minimum non-zero value, will be calculated later
              imageUrl,
              subfolderPath: file.subfolderPath
            };
          })
        );

        // Use the centralized utility to distribute rarity evenly
        const traits = distributeRarityEvenly(rawTraits);

        return {
          id: generateStableLayerId(layer.name),
          name: layer.name,
          order: layer.order,
          traits
        };
      })
    );

    // Sort layers by order
    layers.sort((a, b) => a.order - b.order);

    return {
      layers,
      projectName,
      projectPath
    };
  } catch (error) {
    console.error('Error processing uploaded files:', error);
    throw new Error('Failed to process uploaded files. Please ensure your folder is properly structured.');
  }
};

/**
 * Create a downloadable file
 * @param content Content to download
 * @param fileName File name
 * @param contentType Content type
 */
export const downloadFile = (content: string, fileName: string, contentType: string) => {
  const a = document.createElement('a');
  const file = new Blob([content], { type: contentType });
  a.href = URL.createObjectURL(file);
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};
