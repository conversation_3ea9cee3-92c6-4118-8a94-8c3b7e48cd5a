import React, { ReactNode, useState } from 'react'
import {
  Box,
  AppBar,
  Too<PERSON>bar,
  Typography,
  IconButton,
  Toolt<PERSON>,
  Stack,
  Button
} from '@mui/material'
import {
  SettingsRounded,
  RuleRounded,
  AutoAwesomeRounded,
  FolderOpenRounded
} from '@mui/icons-material'
import { useAppStore } from '../../../application/stores/appStore'
import { SettingsModal } from '../modals/SettingsModal'
import { RulesModal } from '../modals/RulesModal'

interface AppLayoutProps {
  children: ReactNode
}

export const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const currentProject = useAppStore((state) => state.currentProject)
  const [settingsOpen, setSettingsOpen] = useState(false)
  const [rulesOpen, setRulesOpen] = useState(false)

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      minHeight: '100vh',
      bgcolor: 'background.default'
    }}>
      {/* Top Toolbar - V1 Style */}
      <AppBar
        position="static"
        elevation={0}
        sx={{
          bgcolor: 'background.paper',
          borderBottom: '1px solid',
          borderColor: 'divider',
          color: 'text.primary'
        }}
      >
        <Toolbar sx={{ minHeight: '48px !important', px: 2 }}>
          {/* App Title */}
          <Typography
            variant="h6"
            component="div"
            sx={{
              flexGrow: 1,
              fontSize: '14px',
              fontWeight: 600,
              color: 'text.primary'
            }}
          >
            NFT Generator
            {currentProject && (
              <Typography
                variant="body2"
                component="span"
                sx={{
                  ml: 1,
                  opacity: 0.7,
                  fontSize: '12px',
                  fontWeight: 400
                }}
              >
                - {currentProject.name}
              </Typography>
            )}
          </Typography>

          {/* Action Buttons - V1 Style */}
          <Stack direction="row" spacing={1}>
            <Button
              size="small"
              startIcon={<SettingsRounded />}
              onClick={() => setSettingsOpen(true)}
              sx={{
                minWidth: 'auto',
                px: 1.5,
                py: 0.5,
                fontSize: '11px',
                textTransform: 'uppercase',
                fontWeight: 600
              }}
            >
              {currentProject?.name || 'Collection Settings'}
            </Button>

            <Button
              size="small"
              startIcon={<RuleRounded />}
              onClick={() => setRulesOpen(true)}
              sx={{
                minWidth: 'auto',
                px: 1.5,
                py: 0.5,
                fontSize: '11px',
                textTransform: 'uppercase',
                fontWeight: 600
              }}
            >
              Rules
            </Button>

            <Button
              size="small"
              variant="contained"
              startIcon={<AutoAwesomeRounded />}
              sx={{
                minWidth: 'auto',
                px: 1.5,
                py: 0.5,
                fontSize: '11px',
                textTransform: 'uppercase',
                fontWeight: 600,
                bgcolor: 'primary.main',
                '&:hover': {
                  bgcolor: 'primary.dark'
                }
              }}
            >
              Generate
            </Button>
          </Stack>
        </Toolbar>
      </AppBar>

      {/* Main Content Area */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          height: 'calc(100vh - 48px)', // Full height minus header
          display: 'flex',
          bgcolor: 'background.default',
          overflow: 'hidden'
        }}
      >
        {children}
      </Box>

      {/* Modals */}
      <SettingsModal
        open={settingsOpen}
        onClose={() => setSettingsOpen(false)}
      />
      <RulesModal
        open={rulesOpen}
        onClose={() => setRulesOpen(false)}
      />
    </Box>
  )
}
