import React, { useState } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  <PERSON>alogContent,
  DialogActions,
  <PERSON>ton,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Divider,
  Slider,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Stack,
  IconButton,
  Alert,
  Tabs,
  Tab,
  InputAdornment,
  Grid
} from '@mui/material'
import {
  Close as CloseIcon,
  Settings as SettingsIcon,
  Collections as CollectionsIcon,
  Image as ImageIcon,
  Folder as FolderIcon
} from '@mui/icons-material'
import { useAppStore } from '../../../application/stores/appStore'
import { Project } from '../../../domain/entities/Project'
import type { ProjectSettings } from '../../../shared/types/Project.types'

interface SettingsModalProps {
  open: boolean
  onClose: () => void
}

interface CollectionSettings extends ProjectSettings {
  // Basic Settings
  collectionName: string
  collectionSymbol: string
  totalSupply: number
  startTokenId: number
  baseUri: string
  royaltyPercentage: number
  description: string

  // Output Settings
  compressImages: boolean
  outputFolder: string
  includeMetadata: boolean
  metadataFormat: 'opensea' | 'foundation' | 'custom'
}

const defaultCollectionSettings: CollectionSettings = {
  // Basic Settings
  collectionName: 'My NFT Collection',
  collectionSymbol: 'MNC',
  totalSupply: 1000,
  startTokenId: 1,
  baseUri: 'ipfs://',
  royaltyPercentage: 5,
  description: '',

  // Image Settings (from ProjectSettings)
  collectionSize: 1000,
  imageFormat: 'png',
  imageQuality: 90,
  outputResolution: {
    width: 1000,
    height: 1000
  },
  backgroundColor: '#FFFFFF',
  enableDuplicateCheck: true,
  enableRarityCalculation: true,

  // Output Settings
  compressImages: false,
  outputFolder: '',
  includeMetadata: true,
  metadataFormat: 'opensea'
}

export const SettingsModal: React.FC<SettingsModalProps> = ({
  open,
  onClose
}) => {
  const currentProject = useAppStore((state) => state.currentProject)
  const updateProject = useAppStore((state) => state.updateProject)
  const [activeTab, setActiveTab] = useState(0)
  const [settings, setSettings] = useState<CollectionSettings>(defaultCollectionSettings)

  // Update settings when project changes
  React.useEffect(() => {
    if (currentProject?.settings) {
      setSettings(prev => ({
        ...prev,
        ...currentProject.settings,
        // Add default values for new fields if not present
        collectionName: currentProject.name || prev.collectionName,
        collectionSymbol: prev.collectionSymbol,
        totalSupply: currentProject.settings.collectionSize || prev.totalSupply,
        startTokenId: prev.startTokenId,
        baseUri: prev.baseUri,
        royaltyPercentage: prev.royaltyPercentage,
        description: currentProject.description || prev.description,
        compressImages: prev.compressImages,
        outputFolder: prev.outputFolder,
        includeMetadata: prev.includeMetadata,
        metadataFormat: prev.metadataFormat
      }))
    }
  }, [currentProject])

  const handleSettingChange = <K extends keyof CollectionSettings>(
    key: K,
    value: CollectionSettings[K]
  ) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleResolutionChange = (dimension: 'width' | 'height', value: number) => {
    setSettings(prev => ({
      ...prev,
      outputResolution: {
        ...prev.outputResolution,
        [dimension]: value
      }
    }))
  }

  const handleSave = () => {
    if (currentProject) {
      // Update project settings with the core ProjectSettings fields
      const projectSettings: ProjectSettings = {
        collectionSize: settings.totalSupply,
        imageFormat: settings.imageFormat,
        imageQuality: settings.imageQuality,
        outputResolution: settings.outputResolution,
        backgroundColor: settings.backgroundColor,
        enableDuplicateCheck: settings.enableDuplicateCheck,
        enableRarityCalculation: settings.enableRarityCalculation
      }

      // Update project name and description from collection settings
      const updatedProject = new Project(currentProject)
      updatedProject.name = settings.collectionName
      updatedProject.description = settings.description
      updatedProject.settings = projectSettings
      updatedProject.touch()

      // Update project in store to trigger persistence and UI updates
      updateProject(updatedProject)
      console.log('Collection settings saved:', settings)
    }
    onClose()
  }

  const handleReset = () => {
    setSettings(defaultCollectionSettings)
  }

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={false}
      PaperProps={{
        sx: {
          borderRadius: 2,
          width: '70vw',
          maxWidth: '1000px',
          minWidth: '800px',
          height: '80vh',
          maxHeight: '800px',
          minHeight: '700px',
          display: 'flex',
          flexDirection: 'column'
        }
      }}
    >
      {/* Header */}
      <DialogTitle sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        pb: 1
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CollectionsIcon sx={{ color: 'primary.main' }} />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Collection Settings
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Divider />

      {/* Content */}
      <DialogContent sx={{ py: 0, flex: 1, overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
        {/* Info */}
        {!currentProject && (
          <Alert severity="warning" sx={{ m: 3, mb: 2 }}>
            No project selected. Create or open a project to modify collection settings.
          </Alert>
        )}

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange} aria-label="collection settings tabs">
            <Tab label="Basic Settings" />
            <Tab label="Output Settings" />
          </Tabs>
        </Box>

        {/* Tab Content */}
        <Box sx={{ p: 3, flex: 1, overflow: 'auto' }}>
          {/* Basic Settings Tab */}
          {activeTab === 0 && (
            <Stack spacing={3}>
              {/* Collection Info */}
              <Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                  Collection Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Collection Name"
                      size="small"
                      fullWidth
                      value={settings.collectionName}
                      onChange={(e) => handleSettingChange('collectionName', e.target.value)}
                      placeholder="My NFT Collection"
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Collection Symbol"
                      size="small"
                      fullWidth
                      value={settings.collectionSymbol}
                      onChange={(e) => handleSettingChange('collectionSymbol', e.target.value.toUpperCase())}
                      placeholder="MNC"
                      inputProps={{ maxLength: 10 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Total Supply"
                      type="number"
                      size="small"
                      fullWidth
                      value={settings.totalSupply}
                      onChange={(e) => handleSettingChange('totalSupply', parseInt(e.target.value) || 1000)}
                      inputProps={{ min: 1, max: 100000 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Start Token ID"
                      type="number"
                      size="small"
                      fullWidth
                      value={settings.startTokenId}
                      onChange={(e) => handleSettingChange('startTokenId', parseInt(e.target.value) || 1)}
                      inputProps={{ min: 0, max: 10000 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Base URI"
                      size="small"
                      fullWidth
                      value={settings.baseUri}
                      onChange={(e) => handleSettingChange('baseUri', e.target.value)}
                      placeholder="ipfs://"
                      helperText="Base URI for token metadata"
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Royalty Percentage"
                      type="number"
                      size="small"
                      fullWidth
                      value={settings.royaltyPercentage}
                      onChange={(e) => handleSettingChange('royaltyPercentage', parseFloat(e.target.value) || 5)}
                      inputProps={{ min: 0, max: 100, step: 0.1 }}
                      InputProps={{
                        endAdornment: <InputAdornment position="end">%</InputAdornment>,
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      label="Description"
                      multiline
                      rows={3}
                      size="small"
                      fullWidth
                      value={settings.description}
                      onChange={(e) => handleSettingChange('description', e.target.value)}
                      placeholder="Describe your NFT collection..."
                    />
                  </Grid>
                </Grid>
              </Box>
            </Stack>
          )}

          {/* Output Settings Tab */}
          {activeTab === 1 && (
            <Stack spacing={3}>
              {/* Image Settings */}
              <Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                  Image Settings
                </Typography>
                <Stack spacing={2}>
                  <Stack direction="row" spacing={2}>
                    <TextField
                      label="Output Width"
                      type="number"
                      size="small"
                      value={settings.outputResolution.width}
                      onChange={(e) => handleResolutionChange('width', parseInt(e.target.value) || 1000)}
                      inputProps={{ min: 100, max: 4000 }}
                      InputProps={{
                        endAdornment: <InputAdornment position="end">px</InputAdornment>,
                      }}
                    />
                    <TextField
                      label="Output Height"
                      type="number"
                      size="small"
                      value={settings.outputResolution.height}
                      onChange={(e) => handleResolutionChange('height', parseInt(e.target.value) || 1000)}
                      inputProps={{ min: 100, max: 4000 }}
                      InputProps={{
                        endAdornment: <InputAdornment position="end">px</InputAdornment>,
                      }}
                    />
                  </Stack>

                  <FormControl size="small" fullWidth>
                    <InputLabel>Image Format</InputLabel>
                    <Select
                      value={settings.imageFormat}
                      label="Image Format"
                      onChange={(e) => handleSettingChange('imageFormat', e.target.value as any)}
                    >
                      <MenuItem value="png">PNG</MenuItem>
                      <MenuItem value="jpg">JPG</MenuItem>
                      <MenuItem value="webp">WebP</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.compressImages}
                        onChange={(e) => handleSettingChange('compressImages', e.target.checked)}
                      />
                    }
                    label="Compress Images"
                  />

                  {settings.compressImages && (
                    <Box>
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        Image Quality: {settings.imageQuality}%
                      </Typography>
                      <Slider
                        value={settings.imageQuality}
                        onChange={(_, value) => handleSettingChange('imageQuality', value as number)}
                        min={50}
                        max={100}
                        step={5}
                        marks={[
                          { value: 50, label: '50%' },
                          { value: 75, label: '75%' },
                          { value: 100, label: '100%' }
                        ]}
                      />
                    </Box>
                  )}
                </Stack>
              </Box>

              <Divider />

              {/* Output Settings */}
              <Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                  Output Settings
                </Typography>
                <Stack spacing={2}>
                  <TextField
                    label="Output Folder"
                    size="small"
                    fullWidth
                    value={settings.outputFolder}
                    onChange={(e) => handleSettingChange('outputFolder', e.target.value)}
                    placeholder="Select output folder..."
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton size="small">
                            <FolderIcon />
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.includeMetadata}
                        onChange={(e) => handleSettingChange('includeMetadata', e.target.checked)}
                      />
                    }
                    label="Include Metadata"
                  />

                  {settings.includeMetadata && (
                    <FormControl size="small" fullWidth>
                      <InputLabel>Metadata Format</InputLabel>
                      <Select
                        value={settings.metadataFormat}
                        label="Metadata Format"
                        onChange={(e) => handleSettingChange('metadataFormat', e.target.value as any)}
                      >
                        <MenuItem value="opensea">OpenSea</MenuItem>
                        <MenuItem value="foundation">Foundation</MenuItem>
                        <MenuItem value="custom">Custom</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                </Stack>
              </Box>
            </Stack>
          )}


        </Box>
      </DialogContent>

      {/* Actions */}
      <DialogActions sx={{ px: 3, pb: 3, flexShrink: 0 }}>
        <Button onClick={handleReset} color="inherit">
          Reset to Defaults
        </Button>
        <Box sx={{ flexGrow: 1 }} />
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={!currentProject}
        >
          Save Collection Settings
        </Button>
      </DialogActions>
    </Dialog>
  )
}
