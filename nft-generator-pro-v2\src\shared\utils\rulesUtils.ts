/**
 * Rules utility functions - V1 Compatible
 * Based on V1 Rules system utilities
 */

import { Rule, RuleConflict, RuleValidationResult } from '../types/Rules.types'

/**
 * Validate a single rule
 */
export const validateRule = (rule: Rule): RuleValidationResult => {
  const warnings: string[] = []
  const errors: string[] = []

  // Basic validation
  if (!rule.name.trim()) {
    errors.push('Rule name is required')
  } else if (rule.name.trim().length < 3) {
    errors.push('Rule name must be at least 3 characters long')
  }

  if (!rule.type) {
    errors.push('Rule type is required')
  }

  // Type-specific validation
  switch (rule.type) {
    case 'IF_THEN':
      if (!rule.conditions || rule.conditions.length < 2) {
        errors.push('IF_THEN rules require at least 2 conditions (IF and THEN)')
      } else {
        const ifConditions = rule.conditions.filter(c => c.layerId && !c.targetLayerId)
        const thenConditions = rule.conditions.filter(c => c.targetLayerId)

        if (ifConditions.length === 0) {
          errors.push('IF_THEN rules require at least one IF condition')
        }

        if (thenConditions.length === 0) {
          errors.push('IF_THEN rules require at least one THEN condition')
        }
      }
      break

    case 'LAYER_GROUP':
      if (!rule.groupLayers || rule.groupLayers.length < 2) {
        errors.push('Layer group rules require at least 2 layers')
      }
      break

    case 'TRAIT_GROUP':
      if (!rule.groupTraits || rule.groupTraits.length === 0) {
        errors.push('Trait group rules require at least one trait group')
      }
      break
  }

  return {
    isValid: errors.length === 0,
    warnings,
    errors
  }
}

/**
 * Detect conflicts between rules
 */
export const detectRuleConflicts = (rules: Rule[]): RuleConflict[] => {
  const conflicts: RuleConflict[] = []

  // Check for duplicate names
  const nameGroups = rules.reduce((acc, rule) => {
    if (!acc[rule.name]) acc[rule.name] = []
    acc[rule.name].push(rule.id)
    return acc
  }, {} as Record<string, string[]>)

  Object.entries(nameGroups).forEach(([name, ruleIds]) => {
    if (ruleIds.length > 1) {
      conflicts.push({
        severity: 'warning',
        message: `Multiple rules with the same name: "${name}"`,
        ruleIds
      })
    }
  })

  // Check for conflicting IF_THEN rules
  const ifThenRules = rules.filter(r => r.type === 'IF_THEN' && r.enabled)

  for (let i = 0; i < ifThenRules.length; i++) {
    for (let j = i + 1; j < ifThenRules.length; j++) {
      const rule1 = ifThenRules[i]
      const rule2 = ifThenRules[j]

      // Check if rules have conflicting conditions
      const conflict = checkIfThenConflict(rule1, rule2)
      if (conflict) {
        conflicts.push({
          severity: 'error',
          message: conflict,
          ruleIds: [rule1.id, rule2.id]
        })
      }
    }
  }

  // Check for layer group conflicts
  const layerGroupRules = rules.filter(r => r.type === 'LAYER_GROUP' && r.enabled)

  for (let i = 0; i < layerGroupRules.length; i++) {
    for (let j = i + 1; j < layerGroupRules.length; j++) {
      const rule1 = layerGroupRules[i]
      const rule2 = layerGroupRules[j]

      if (rule1.groupLayers && rule2.groupLayers) {
        const overlap = rule1.groupLayers.filter(layer => rule2.groupLayers!.includes(layer))
        if (overlap.length > 0) {
          conflicts.push({
            severity: 'warning',
            message: `Layer groups overlap: ${overlap.join(', ')}`,
            ruleIds: [rule1.id, rule2.id],
            affectedLayers: overlap
          })
        }
      }
    }
  }

  return conflicts
}

/**
 * Check for conflicts between two IF_THEN rules
 */
const checkIfThenConflict = (rule1: Rule, rule2: Rule): string | null => {
  if (!rule1.conditions || !rule2.conditions) return null

  const rule1If = rule1.conditions.filter(c => c.layerId && !c.targetLayerId)
  const rule1Then = rule1.conditions.filter(c => c.targetLayerId)

  const rule2If = rule2.conditions.filter(c => c.layerId && !c.targetLayerId)
  const rule2Then = rule2.conditions.filter(c => c.targetLayerId)

  // Check if same IF conditions lead to conflicting THEN conditions
  const sameIfConditions = rule1If.some(if1 =>
    rule2If.some(if2 =>
      if1.layerId === if2.layerId && if1.traitId === if2.traitId
    )
  )

  if (sameIfConditions) {
    const conflictingThen = rule1Then.some(then1 =>
      rule2Then.some(then2 =>
        then1.targetLayerId === then2.targetLayerId &&
        then1.targetTraitId === then2.targetTraitId &&
        then1.operator !== then2.operator
      )
    )

    if (conflictingThen) {
      return `Rules have same IF conditions but conflicting THEN conditions`
    }
  }

  return null
}



/**
 * Get rule summary for display
 */
export const getRuleSummary = (rule: Rule): string => {
  switch (rule.type) {
    case 'LAYER_GROUP':
      if (!rule.groupLayers || rule.groupLayers.length < 2) {
        return "Incomplete layer group rule"
      }
      return `Layer Group: ${rule.groupLayers.join(', ')}`

    case 'TRAIT_GROUP':
      if (!rule.groupTraits || rule.groupTraits.length < 1) {
        return "Incomplete trait group rule"
      }
      const traitSummary = rule.groupTraits.map(group =>
        `${group.layerId} (${group.traitIds.length} traits)`
      )
      return `Trait Group: ${traitSummary.join(', ')}`

    case 'IF_THEN':
    default:
      if (!rule.conditions || rule.conditions.length < 2) {
        return "Incomplete rule"
      }

      const ifConditions = rule.conditions.filter(c => c.layerId && !c.targetLayerId)
      const thenConditions = rule.conditions.filter(c => c.targetLayerId)

      if (ifConditions.length === 0 || thenConditions.length === 0) {
        return "Incomplete rule"
      }

      let summary = `If ${ifConditions[0].layerId} has ${ifConditions[0].traitId}`

      // Add additional IF conditions
      for (let i = 1; i < ifConditions.length; i++) {
        const logicalOp = ifConditions[i].logicalOperator === 'OR' ? 'OR' : 'AND'
        summary += ` ${logicalOp} ${ifConditions[i].layerId} has ${ifConditions[i].traitId}`
      }

      const firstThen = thenConditions[0]
      const operatorText = firstThen.operator === 'CANNOT_HAVE' ? 'cannot have' : 'must have'
      summary += `, then ${firstThen.targetLayerId} ${operatorText} ${firstThen.targetTraitId}`

      if (thenConditions.length > 1) {
        summary += ` (+ ${thenConditions.length - 1} more conditions)`
      }

      return summary
  }
}

/**
 * Sort rules by order (lowest to highest)
 */
export const sortRulesByOrder = (rules: Rule[]): Rule[] => {
  return [...rules].sort((a, b) => a.order - b.order)
}

/**
 * Generate unique rule ID
 */
export const generateRuleId = (): string => {
  return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Create default rule based on type
 */
export const createDefaultRule = (type: Rule['type']): Partial<Rule> => {
  const baseRule = {
    id: generateRuleId(),
    name: '',
    description: '',
    type,
    conditions: [],
    order: 1,
    enabled: true
  }

  switch (type) {
    case 'LAYER_GROUP':
      return {
        ...baseRule,
        groupLayers: [],
        groupBehavior: 'sync'
      }

    case 'TRAIT_GROUP':
      return {
        ...baseRule,
        groupTraits: [],
        groupBehavior: 'sync'
      }

    case 'IF_THEN':
    default:
      return baseRule
  }
}
