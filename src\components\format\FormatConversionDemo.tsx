import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  LinearProgress,
  Card,
  CardMedia,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Skeleton,
  Tabs,
  Tab
} from '@mui/material';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import CompareIcon from '@mui/icons-material/Compare';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import SaveAltIcon from '@mui/icons-material/SaveAlt';
import FormatConversionSettings from './FormatConversionSettings';
import { EnhancedConversionOptions } from '@/services/image/enhanced-conversion-types';
import { unifiedImageService } from '@/services/image/unified-image.service';

/**
 * Demo component for the Enhanced Format Conversion system
 * Part of F3-02: Format Conversion Optimization
 */
const FormatConversionDemo: React.FC = () => {
  // File state
  const [originalFile, setOriginalFile] = useState<File | null>(null);
  const [originalUrl, setOriginalUrl] = useState<string | null>(null);
  const [originalSize, setOriginalSize] = useState<number>(0);
  const [originalDimensions, setOriginalDimensions] = useState<{ width: number, height: number } | null>(null);

  // Conversion state
  const [conversionOptions, setConversionOptions] = useState<EnhancedConversionOptions>({
    analyzeContent: true,
    adaptiveCompression: true,
    compressionStrategy: 'balanced',
    metadataStrategy: 'minimal'
  });
  const [converting, setConverting] = useState<boolean>(false);
  const [conversionResult, setConversionResult] = useState<any | null>(null);
  const [conversionError, setConversionError] = useState<string | null>(null);
  const [convertedUrl, setConvertedUrl] = useState<string | null>(null);

  // UI state
  const [activeTab, setActiveTab] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];

      // Only accept image files
      if (!file.type.startsWith('image/')) {
        setConversionError('Please select an image file');
        return;
      }

      setOriginalFile(file);
      setOriginalSize(file.size);
      setConversionResult(null);
      setConversionError(null);
      setConvertedUrl(null);

      // Create URL for the image
      const url = URL.createObjectURL(file);
      setOriginalUrl(url);

      // Get image dimensions
      const img = new Image();
      img.onload = () => {
        setOriginalDimensions({
          width: img.naturalWidth,
          height: img.naturalHeight
        });
      };
      img.src = url;
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Convert the image
  const convertImage = async () => {
    if (!originalFile) return;

    try {
      setConverting(true);
      setConversionError(null);

      // Read the file as ArrayBuffer
      const arrayBuffer = await originalFile.arrayBuffer();

      // Get the format from the file type
      const format = originalFile.type.split('/')[1];

      // Convert the image
      const result = await unifiedImageService.optimizeConversion(
        arrayBuffer,
        format,
        conversionOptions
      );

      // Handle conversion result
      if (result.success) {
        setConversionResult(result);

        // SECURITY FIX: Create data URL instead of blob URL
        const blob = new Blob([result.buffer], { type: `image/${result.format}` });
        const dataUrl = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = () => reject(reader.error);
          reader.readAsDataURL(blob);
        });
        setConvertedUrl(dataUrl);
      } else {
        setConversionError('Conversion failed');
      }
    } catch (error) {
      console.error('Conversion error:', error);
      setConversionError('An error occurred during conversion');
    } finally {
      setConverting(false);
    }
  };

  // Reset the demo
  const resetDemo = () => {
    if (originalUrl) URL.revokeObjectURL(originalUrl);
    if (convertedUrl) URL.revokeObjectURL(convertedUrl);

    setOriginalFile(null);
    setOriginalUrl(null);
    setOriginalSize(0);
    setOriginalDimensions(null);
    setConversionResult(null);
    setConversionError(null);
    setConvertedUrl(null);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Download converted image
  const downloadConvertedImage = () => {
    if (!convertedUrl || !conversionResult) return;

    const a = document.createElement('a');
    a.href = convertedUrl;
    a.download = `converted_image.${conversionResult.format}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  // Format bytes to readable size
  const formatBytes = (bytes: number, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };

  // Clean up URLs on unmount
  useEffect(() => {
    return () => {
      if (originalUrl) URL.revokeObjectURL(originalUrl);
      if (convertedUrl) URL.revokeObjectURL(convertedUrl);
    };
  }, [originalUrl, convertedUrl]);

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 2 }}>
      <Typography variant="h4" gutterBottom>
        Format Conversion Demo
      </Typography>
      <Typography variant="subtitle1" color="text.secondary" gutterBottom>
        Test the enhanced format conversion system with your own images
      </Typography>

      {/* File Upload Section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={8}>
            <Typography variant="h6" gutterBottom>
              1. Select an image to convert
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Upload an image file to see the enhanced format conversion in action
            </Typography>
            <Button
              variant="contained"
              component="label"
              startIcon={<UploadFileIcon />}
              sx={{ mt: 1 }}
            >
              Choose Image
              <input
                type="file"
                hidden
                accept="image/*"
                onChange={handleFileSelect}
                ref={fileInputRef}
              />
            </Button>

            {originalFile && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                Selected: {originalFile.name} ({formatBytes(originalSize)})
                {originalDimensions && ` - ${originalDimensions.width}×${originalDimensions.height}`}
              </Typography>
            )}
          </Grid>

          <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
            {originalUrl ? (
              <Box
                component="img"
                src={originalUrl}
                alt="Original image"
                sx={{
                  maxWidth: '100%',
                  maxHeight: 150,
                  objectFit: 'contain',
                  border: '1px solid #ddd',
                  borderRadius: 1
                }}
              />
            ) : (
              <Skeleton variant="rectangular" width="100%" height={150} />
            )}
          </Grid>
        </Grid>
      </Paper>

      {/* Conversion Settings */}
      {originalFile && (
        <>
          <Typography variant="h6" gutterBottom>
            2. Configure conversion settings
          </Typography>

          <FormatConversionSettings
            onChange={setConversionOptions}
            defaultOptions={conversionOptions}
          />

          <Box sx={{ mt: 2, mb: 3, display: 'flex', justifyContent: 'center' }}>
            <Button
              variant="contained"
              color="primary"
              onClick={convertImage}
              disabled={converting || !originalFile}
              startIcon={<CompareIcon />}
              sx={{ mr: 2 }}
            >
              Convert Image
            </Button>

            <Button
              variant="outlined"
              onClick={resetDemo}
              startIcon={<RestartAltIcon />}
            >
              Reset
            </Button>
          </Box>

          {converting && (
            <Box sx={{ width: '100%', mb: 3 }}>
              <LinearProgress />
              <Typography variant="body2" align="center" sx={{ mt: 1 }}>
                Converting image...
              </Typography>
            </Box>
          )}

          {conversionError && (
            <Paper sx={{ p: 2, mb: 3, bgcolor: '#ffebee' }}>
              <Typography color="error">{conversionError}</Typography>
            </Paper>
          )}
        </>
      )}

      {/* Conversion Results */}
      {conversionResult && convertedUrl && (
        <Box sx={{ mt: 4 }}>
          <Typography variant="h6" gutterBottom>
            3. Conversion Results
          </Typography>

          <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
            <Tab label="Comparison" />
            <Tab label="Technical Details" />
            <Tab label="Optimizations Applied" />
          </Tabs>

          {/* Comparison Tab */}
          {activeTab === 0 && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardMedia
                    component="img"
                    image={originalUrl || ''}
                    alt="Original image"
                    sx={{ maxHeight: 300, objectFit: 'contain' }}
                  />
                  <CardContent>
                    <Typography variant="h6">Original</Typography>
                    <Typography variant="body2">
                      Format: {originalFile?.type.split('/')[1].toUpperCase()}
                    </Typography>
                    <Typography variant="body2">
                      Size: {formatBytes(originalSize)}
                    </Typography>
                    {originalDimensions && (
                      <Typography variant="body2">
                        Dimensions: {originalDimensions.width}×{originalDimensions.height}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardMedia
                    component="img"
                    image={convertedUrl}
                    alt="Converted image"
                    sx={{ maxHeight: 300, objectFit: 'contain' }}
                  />
                  <CardContent>
                    <Typography variant="h6">Converted</Typography>
                    <Typography variant="body2">
                      Format: {conversionResult.format.toUpperCase()}
                    </Typography>
                    <Typography variant="body2">
                      Size: {formatBytes(conversionResult.newSize)}
                    </Typography>
                    <Typography variant="body2">
                      Dimensions: {conversionResult.width}×{conversionResult.height}
                    </Typography>
                    <Typography variant="body2" color={conversionResult.compressionRatio > 1 ? 'success.main' : 'inherit'}>
                      Compression Ratio: {conversionResult.compressionRatio.toFixed(2)}x
                    </Typography>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<SaveAltIcon />}
                      onClick={downloadConvertedImage}
                      sx={{ mt: 1 }}
                    >
                      Download
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12}>
                <Paper sx={{ p: 2, bgcolor: '#e8f5e9' }}>
                  <Typography variant="body1" gutterBottom>
                    Size Reduction: {formatBytes(originalSize - conversionResult.newSize)} ({Math.round((1 - conversionResult.newSize / originalSize) * 100)}%)
                  </Typography>
                  {conversionResult.speedGain && (
                    <Typography variant="body2">
                      Estimated Load Time Improvement: {(conversionResult.speedGain * 1000).toFixed(0)}ms
                    </Typography>
                  )}
                </Paper>
              </Grid>
            </Grid>
          )}

          {/* Technical Details Tab */}
          {activeTab === 1 && (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Property</TableCell>
                    <TableCell>Original</TableCell>
                    <TableCell>Converted</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell>Format</TableCell>
                    <TableCell>{originalFile?.type.split('/')[1].toUpperCase()}</TableCell>
                    <TableCell>{conversionResult.format.toUpperCase()}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>File Size</TableCell>
                    <TableCell>{formatBytes(originalSize)}</TableCell>
                    <TableCell>{formatBytes(conversionResult.newSize)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Dimensions</TableCell>
                    <TableCell>
                      {originalDimensions ? `${originalDimensions.width}×${originalDimensions.height}` : 'Unknown'}
                    </TableCell>
                    <TableCell>{conversionResult.width}×{conversionResult.height}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Compression Ratio</TableCell>
                    <TableCell>-</TableCell>
                    <TableCell>{conversionResult.compressionRatio.toFixed(2)}x</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Processing Time</TableCell>
                    <TableCell>-</TableCell>
                    <TableCell>{conversionResult.processingTime}ms</TableCell>
                  </TableRow>

                  {conversionResult.contentAnalysisResults && (
                    <>
                      <TableRow>
                        <TableCell colSpan={3} sx={{ bgcolor: '#f5f5f5' }}>
                          <Typography variant="subtitle2">Content Analysis</Typography>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Text Content</TableCell>
                        <TableCell colSpan={2}>
                          {Math.round(conversionResult.contentAnalysisResults.textContent * 100)}%
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Gradient Content</TableCell>
                        <TableCell colSpan={2}>
                          {Math.round(conversionResult.contentAnalysisResults.gradientContent * 100)}%
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Edge Complexity</TableCell>
                        <TableCell colSpan={2}>
                          {Math.round(conversionResult.contentAnalysisResults.edgeComplexity * 100)}%
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Noise Level</TableCell>
                        <TableCell colSpan={2}>
                          {Math.round(conversionResult.contentAnalysisResults.noiseLevel * 100)}%
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Color Distribution</TableCell>
                        <TableCell colSpan={2}>
                          {conversionResult.contentAnalysisResults.colorDistribution}
                        </TableCell>
                      </TableRow>
                    </>
                  )}

                  {conversionResult.metadataStatus && (
                    <>
                      <TableRow>
                        <TableCell colSpan={3} sx={{ bgcolor: '#f5f5f5' }}>
                          <Typography variant="subtitle2">Metadata</Typography>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Preserved Tags</TableCell>
                        <TableCell colSpan={2}>
                          {conversionResult.metadataStatus.preserved.length > 0
                            ? conversionResult.metadataStatus.preserved.join(', ')
                            : 'None'}
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Removed Tags</TableCell>
                        <TableCell colSpan={2}>
                          {conversionResult.metadataStatus.removed.length > 0
                            ? conversionResult.metadataStatus.removed.join(', ')
                            : 'None'}
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Metadata Size Reduction</TableCell>
                        <TableCell colSpan={2}>
                          {formatBytes(conversionResult.metadataStatus.sizeReduction)}
                        </TableCell>
                      </TableRow>
                    </>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {/* Optimizations Tab */}
          {activeTab === 2 && (
            <Paper sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Optimization Techniques Applied
              </Typography>

              <Box component="ul" sx={{ pl: 2 }}>
                {conversionResult.optimizationTechniquesApplied.map((technique: string, index: number) => (
                  <Box component="li" key={index} sx={{ mb: 1 }}>
                    <Typography variant="body1">
                      {technique.split('-').map(word =>
                        word.charAt(0).toUpperCase() + word.slice(1)
                      ).join(' ')}
                    </Typography>

                    {technique === 'content-analysis' && (
                      <Typography variant="body2" color="text.secondary">
                        Analyzed image content to optimize conversion settings
                      </Typography>
                    )}

                    {technique === 'format-optimization' && (
                      <Typography variant="body2" color="text.secondary">
                        Selected optimal format based on image content
                      </Typography>
                    )}

                    {technique === 'content-specific-optimization' && (
                      <Typography variant="body2" color="text.secondary">
                        Applied specialized settings based on detected content type
                      </Typography>
                    )}

                    {technique === 'adaptive-compression' && (
                      <Typography variant="body2" color="text.secondary">
                        Dynamically adjusted compression based on content complexity
                      </Typography>
                    )}

                    {technique === 'metadata-optimization' && (
                      <Typography variant="body2" color="text.secondary">
                        Optimized metadata to reduce file size while preserving essential information
                      </Typography>
                    )}

                    {technique === 'format-specific-enhancements' && (
                      <Typography variant="body2" color="text.secondary">
                        Applied format-specific optimizations for {conversionResult.format.toUpperCase()}
                      </Typography>
                    )}
                  </Box>
                ))}
              </Box>
            </Paper>
          )}
        </Box>
      )}
    </Box>
  );
};

export default FormatConversionDemo;