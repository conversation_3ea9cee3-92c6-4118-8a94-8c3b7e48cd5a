/**
 * Lightweight UI state persistence using localStorage
 * Only stores essential UI state, not large project data
 */

import { UIState } from '../shared/types/Project.types'

const UI_STORAGE_KEY = 'nft-generator-ui-state'

interface PersistedUIState {
  viewMode: UIState['viewMode']
  sortBy: UIState['sortBy']
  sortDirection: UIState['sortDirection']
  showHidden: boolean
  filterText: string
}

/**
 * Save UI state to localStorage
 */
export const saveUIState = (uiState: UIState): void => {
  try {
    const persistedState: PersistedUIState = {
      viewMode: uiState.viewMode,
      sortBy: uiState.sortBy,
      sortDirection: uiState.sortDirection,
      showHidden: uiState.showHidden,
      filterText: uiState.filterText
    }
    
    localStorage.setItem(UI_STORAGE_KEY, JSON.stringify(persistedState))
  } catch (error) {
    console.warn('Failed to save UI state to localStorage:', error)
  }
}

/**
 * Load UI state from localStorage
 */
export const loadUIState = (): Partial<UIState> => {
  try {
    const stored = localStorage.getItem(UI_STORAGE_KEY)
    if (!stored) return {}
    
    const persistedState: PersistedUIState = JSON.parse(stored)
    return {
      viewMode: persistedState.viewMode,
      sortBy: persistedState.sortBy,
      sortDirection: persistedState.sortDirection,
      showHidden: persistedState.showHidden,
      filterText: persistedState.filterText
    }
  } catch (error) {
    console.warn('Failed to load UI state from localStorage:', error)
    return {}
  }
}

/**
 * Clear UI state from localStorage
 */
export const clearUIState = (): void => {
  try {
    localStorage.removeItem(UI_STORAGE_KEY)
  } catch (error) {
    console.warn('Failed to clear UI state from localStorage:', error)
  }
}

/**
 * Check if localStorage is available and has space
 */
export const checkStorageAvailability = (): { available: boolean; quota?: number; used?: number } => {
  try {
    // Test if localStorage is available
    const testKey = '__storage_test__'
    localStorage.setItem(testKey, 'test')
    localStorage.removeItem(testKey)
    
    // Try to estimate storage usage (not accurate but gives an idea)
    let used = 0
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        used += localStorage[key].length + key.length
      }
    }
    
    return {
      available: true,
      used: used,
      quota: 5 * 1024 * 1024 // Estimate 5MB quota
    }
  } catch (error) {
    return {
      available: false
    }
  }
}
