import React, { createContext, useState, useContext, useCallback, useEffect, ReactNode } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Layer, Trait, LayerSubgroup } from '../types/layers';
import { useUI } from './ui/UIContext';
import unifiedStorageService from '@/services/storage/unified-storage.service';
import { DB_CONFIG } from '@/utils/constants/index';

/**
 * Layer context type definition
 */
interface LayersContextType {
  layers: Layer[];
  
  // CRUD operations
  setLayers: (layers: Layer[]) => void;
  addLayer: (layer: Layer) => void;
  updateLayer: (layerId: string, updatedLayer: Layer) => void;
  deleteLayer: (layerId: string) => void;
  reorderLayer: (layerId: string, newOrder: number) => void;
  
  // Rarity locking
  toggleLayerLock: (layerId: string, isLocked: boolean, lockedRarity?: number) => void;
  toggleSubgroupLock: (layerId: string, subgroupId: string, isLocked: boolean, lockedRarity?: number) => void;
  toggleTraitLock: (layerId: string, traitId: string, isLocked: boolean, lockedRarity?: number) => void;
  
  // Rarity calculations
  updateSubgroupWeight: (layerId: string, subgroupId: string, weight: number) => void;
  recalculateLayerRarity: (layer: Layer) => Layer;
  
  // Import operations
  importLayerFromFolder: (folderPath: string) => Promise<Layer>;
}

/**
 * Default context values
 */
const defaultContextValues: LayersContextType = {
  layers: [],
  
  setLayers: () => {},
  addLayer: () => {},
  updateLayer: () => {},
  deleteLayer: () => {},
  reorderLayer: () => {},
  
  toggleLayerLock: () => {},
  toggleSubgroupLock: () => {},
  toggleTraitLock: () => {},
  
  updateSubgroupWeight: () => {},
  recalculateLayerRarity: (layer) => layer,
  
  importLayerFromFolder: async () => {
    return {} as Layer;
  }
};

// Create context
const LayersContext = createContext<LayersContextType>(defaultContextValues);

/**
 * Provider Props
 */
interface LayersProviderProps {
  children: ReactNode;
  initialLayers?: Layer[];
  onLayersChange?: (layers: Layer[]) => void;
}

/**
 * Layers Context Provider component
 */
export const LayersProvider: React.FC<LayersProviderProps> = ({ 
  children, 
  initialLayers = [],
  onLayersChange
}) => {
  // State
  const [layers, setLayersState] = useState<Layer[]>(initialLayers);
  
  // Get UI Context for selection state
  const { selectLayer, state: uiState } = useUI();
  
  // Notify parent component when layers change
  useEffect(() => {
    if (onLayersChange) {
      onLayersChange(layers);
    }
  }, [layers, onLayersChange]);

  // Optimized wrapper for setLayers to implement data saving
  const setLayers = useCallback((newLayers: Layer[]) => {
    setLayersState(newLayers);
    
    // Save to storage if we have a current project
    if (newLayers.length > 0) {
      const timeoutId = setTimeout(() => {
        unifiedStorageService.saveLayers(DB_CONFIG.STORES.LAYERS, newLayers)
          .catch(error => {
            console.error('Error saving layers to storage:', error);
          });
      }, 500);
      
      return () => clearTimeout(timeoutId);
    }
  }, []);
  
  /**
   * Add new layer
   */
  const addLayer = useCallback((layer: Layer) => {
    setLayersState(prevLayers => {
      const updatedLayers = [...prevLayers, layer];
      return updatedLayers;
    });
  }, []);
  
  /**
   * Update existing layer
   */
  const updateLayer = useCallback((layerId: string, updatedLayer: Layer) => {
    setLayersState(prevLayers => 
      prevLayers.map(layer => 
        layer.id === layerId ? updatedLayer : layer
      )
    );
  }, []);
  
  /**
   * Delete layer
   */
  const deleteLayer = useCallback((layerId: string) => {
    setLayersState(prevLayers => prevLayers.filter(layer => layer.id !== layerId));
    
    // If selected layer is deleted, clear the selection
    if (uiState.selectedLayer === layerId) {
      selectLayer(null);
    }
  }, [uiState.selectedLayer, selectLayer]);
  
  /**
   * Reorder layer
   */
  const reorderLayer = useCallback((layerId: string, newOrder: number) => {
    setLayersState(prevLayers => {
      const layerToMove = prevLayers.find(layer => layer.id === layerId);
      if (!layerToMove) return prevLayers;
      
      const oldOrder = layerToMove.order;
      
      // Update layers with new order
      return prevLayers.map(layer => {
        if (layer.id === layerId) {
          return { ...layer, order: newOrder };
        }
        
        // Update other layers' orders
        if (oldOrder < newOrder) {
          // Layer is moving down
          if (layer.order > oldOrder && layer.order <= newOrder) {
            return { ...layer, order: layer.order - 1 };
          }
        } else {
          // Layer is moving up
          if (layer.order >= newOrder && layer.order < oldOrder) {
            return { ...layer, order: layer.order + 1 };
          }
        }
        
        return layer;
      });
    });
  }, []);
  
  /**
   * Normalize subgroup weights (total = 100)
   */
  const normalizeSubgroupWeights = useCallback((subgroups: LayerSubgroup[]): LayerSubgroup[] => {
    // Separate locked and unlocked subgroups
    const lockedSubgroups = subgroups.filter(sg => sg.isLocked);
    const unlockedSubgroups = subgroups.filter(sg => !sg.isLocked);
    
    // Calculate total locked weight
    const totalLockedWeight = lockedSubgroups.reduce((sum, sg) => sum + sg.lockedRarity, 0);
    
    // Calculate remaining weight for unlocked subgroups
    const remainingWeight = Math.max(0, 100 - totalLockedWeight);
    
    // Count unlocked subgroups
    const unlockCount = unlockedSubgroups.length;
    
    // Update subgroups
    const updatedSubgroups = [...subgroups];
    
    // Distribute remaining weight evenly among unlocked subgroups
    if (unlockCount > 0 && remainingWeight > 0) {
      const weightPerSubgroup = remainingWeight / unlockCount;
      
      return updatedSubgroups.map(sg => {
        if (!sg.isLocked) {
          return { ...sg, rarityWeight: weightPerSubgroup };
        } else {
          return { ...sg, rarityWeight: sg.lockedRarity };
        }
      });
    }
    
    return updatedSubgroups;
  }, []);
  
  /**
   * Recalculate layer rarity
   */
  const recalculateLayerRarity = useCallback((layer: Layer): Layer => {
    // If layer is locked, no need to recalculate
    if (layer.isLocked) {
      return layer;
    }
    
    // Normalize subgroup weights to total 100
    const updatedSubgroups = normalizeSubgroupWeights(layer.subgroups);
    
    // Update traits with recalculated rarity
    const updatedTraits = [...layer.traits];
    
    for (const subgroup of updatedSubgroups) {
      // Get traits in this subgroup
      const subgroupTraits = updatedTraits.filter(trait => 
        trait.subgroupId === subgroup.id
      );
      
      // If subgroup is locked, distribute its rarity
      if (subgroup.isLocked) {
        const lockedTraits = subgroupTraits.filter(trait => trait.isLocked);
        const totalLockedRarity = lockedTraits.reduce((sum, trait) => 
          sum + trait.lockedRarity, 0);
        
        // Warn if locked traits exceed subgroup rarity
        if (totalLockedRarity > subgroup.lockedRarity) {
          console.warn(`Subgroup "${subgroup.name}" locked traits exceed subgroup rarity`);
        }
        
        // Calculate remaining rarity for unlocked traits
        const remainingRarity = Math.max(0, subgroup.lockedRarity - totalLockedRarity);
        const unlockTraitsCount = subgroupTraits.filter(trait => !trait.isLocked).length;
        
        if (unlockTraitsCount > 0 && remainingRarity > 0) {
          const rarityPerTrait = remainingRarity / unlockTraitsCount;
          
          // Distribute rarity to unlocked traits
          updatedTraits.forEach((trait, index) => {
            if (trait.subgroupId === subgroup.id && !trait.isLocked) {
              updatedTraits[index] = { ...trait, rarity: rarityPerTrait };
            }
          });
        }
      } else {
        // Subgroup is not locked, distribute rarity based on traits
        const lockedTraits = subgroupTraits.filter(trait => trait.isLocked);
        const totalLockedRarity = lockedTraits.reduce((sum, trait) => 
          sum + trait.lockedRarity, 0);
        
        // Calculate remaining rarity for unlocked traits
        const unlockTraits = subgroupTraits.filter(trait => !trait.isLocked);
        const remainingRarity = Math.max(0, 100 - totalLockedRarity);
        
        if (unlockTraits.length > 0 && remainingRarity > 0) {
          const rarityPerTrait = remainingRarity / unlockTraits.length;
          
          updatedTraits.forEach((trait, index) => {
            if (trait.subgroupId === subgroup.id && !trait.isLocked) {
              updatedTraits[index] = { ...trait, rarity: rarityPerTrait };
            }
          });
        }
      }
    }
    
    return {
      ...layer,
      subgroups: updatedSubgroups,
      traits: updatedTraits
    };
  }, [normalizeSubgroupWeights]);
  
  /**
   * Toggle layer lock
   */
  const toggleLayerLock = useCallback((layerId: string, isLocked: boolean, lockedRarity: number = 100) => {
    setLayersState(prevLayers => {
      const layerIndex = prevLayers.findIndex(l => l.id === layerId);
      if (layerIndex === -1) return prevLayers;
      
      const updatedLayer = {
        ...prevLayers[layerIndex],
        isLocked,
        lockedRarity: isLocked ? lockedRarity : 0
      };
      
      // Recalculate rarity
      const recalculatedLayer = recalculateLayerRarity(updatedLayer);
      
      // Update state
      const updatedLayers = [...prevLayers];
      updatedLayers[layerIndex] = recalculatedLayer;
      return updatedLayers;
    });
  }, [recalculateLayerRarity]);
  
  /**
   * Toggle subgroup lock
   */
  const toggleSubgroupLock = useCallback((layerId: string, subgroupId: string, isLocked: boolean, lockedRarity: number = 0) => {
    setLayersState(prevLayers => {
      const layerIndex = prevLayers.findIndex(l => l.id === layerId);
      if (layerIndex === -1) return prevLayers;
      
      const layer = prevLayers[layerIndex];
      const subgroupIndex = layer.subgroups.findIndex(sg => sg.id === subgroupId);
      if (subgroupIndex === -1) return prevLayers;
      
      // Update subgroup
      const updatedSubgroups = [...layer.subgroups];
      updatedSubgroups[subgroupIndex] = {
        ...updatedSubgroups[subgroupIndex],
        isLocked,
        lockedRarity: isLocked ? lockedRarity : 0
      };
      
      // Update layer
      const updatedLayer = {
        ...layer,
        subgroups: updatedSubgroups
      };
      
      // Recalculate rarity
      const recalculatedLayer = recalculateLayerRarity(updatedLayer);
      
      // Update state
      const updatedLayers = [...prevLayers];
      updatedLayers[layerIndex] = recalculatedLayer;
      return updatedLayers;
    });
  }, [recalculateLayerRarity]);
  
  /**
   * Toggle trait lock
   */
  const toggleTraitLock = useCallback((layerId: string, traitId: string, isLocked: boolean, lockedRarity: number = 0) => {
    setLayersState(prevLayers => {
      const layerIndex = prevLayers.findIndex(l => l.id === layerId);
      if (layerIndex === -1) return prevLayers;
      
      const layer = prevLayers[layerIndex];
      const traitIndex = layer.traits.findIndex(t => t.id === traitId);
      if (traitIndex === -1) return prevLayers;
      
      // Update trait
      const updatedTraits = [...layer.traits];
      updatedTraits[traitIndex] = {
        ...updatedTraits[traitIndex],
        isLocked,
        lockedRarity: isLocked ? lockedRarity : 0
      };
      
      // Update layer
      const updatedLayer = {
        ...layer,
        traits: updatedTraits
      };
      
      // Recalculate rarity
      const recalculatedLayer = recalculateLayerRarity(updatedLayer);
      
      // Update state
      const updatedLayers = [...prevLayers];
      updatedLayers[layerIndex] = recalculatedLayer;
      return updatedLayers;
    });
  }, [recalculateLayerRarity]);
  
  /**
   * Update subgroup weight
   */
  const updateSubgroupWeight = useCallback((layerId: string, subgroupId: string, weight: number) => {
    setLayersState(prevLayers => {
      const layerIndex = prevLayers.findIndex(l => l.id === layerId);
      if (layerIndex === -1) return prevLayers;
      
      const layer = prevLayers[layerIndex];
      const subgroupIndex = layer.subgroups.findIndex(sg => sg.id === subgroupId);
      if (subgroupIndex === -1) return prevLayers;
      
      // Update subgroups with new weight
      const updatedSubgroups = [...layer.subgroups];
      updatedSubgroups[subgroupIndex] = {
        ...updatedSubgroups[subgroupIndex], 
        rarityWeight: weight
      };
      
      // Update layer
      const updatedLayer = {
        ...layer,
        subgroups: updatedSubgroups
      };
      
      // Recalculate rarity
      const recalculatedLayer = recalculateLayerRarity(updatedLayer);
      
      // Update state
      const updatedLayers = [...prevLayers];
      updatedLayers[layerIndex] = recalculatedLayer;
      return updatedLayers;
    });
  }, [recalculateLayerRarity]);
  
  /**
   * Import layer from folder
   */
  const importLayerFromFolder = useCallback(async (folderPath: string): Promise<Layer> => {
    // Real implementation would interact with file system
    // For demo, create a sample layer
    const folderName = folderPath.split('/').pop() || folderPath.split('\\').pop() || 'Layer';
    
    // Create new layer
    const newLayer: Layer = {
      id: uuidv4(),
      name: folderName,
      order: layers.length + 1,
      traits: [],
      subgroups: [],
      isLocked: false,
      lockedRarity: 100,
      rarityDistributionMode: 'auto'
    };
    
    // Create default subgroup
    const defaultSubgroup: LayerSubgroup = {
      id: uuidv4(),
      name: 'Default',
      folderPath: folderPath,
      rarityWeight: 100,
      traits: [],
      isLocked: false,
      lockedRarity: 0
    };
    
    // No sample traits - start with empty layer
    defaultSubgroup.traits = [];

    // Update layer with subgroup but no traits
    newLayer.subgroups = [defaultSubgroup];
    newLayer.traits = [];
    
    return newLayer;
  }, [layers.length]);
  
  /**
   * Context value
   */
  const contextValue: LayersContextType = {
    layers,
    
    setLayers,
    addLayer,
    updateLayer,
    deleteLayer,
    reorderLayer,
    
    toggleLayerLock,
    toggleSubgroupLock,
    toggleTraitLock,
    
    updateSubgroupWeight,
    recalculateLayerRarity,
    
    importLayerFromFolder
  };
  
  return (
    <LayersContext.Provider value={contextValue}>
      {children}
    </LayersContext.Provider>
  );
};

/**
 * Layers context hook
 */
export const useLayers = () => {
  const context = useContext(LayersContext);
  
  if (context === undefined) {
    throw new Error('useLayers must be used within a LayersProvider');
  }
  
  return context;
};

export default LayersContext;