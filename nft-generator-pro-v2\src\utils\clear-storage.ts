/**
 * Utility to clear all storage and fix quota issues
 */

/**
 * Clear all localStorage data
 */
export const clearAllLocalStorage = (): void => {
  try {
    // Get all keys before clearing
    const keys = Object.keys(localStorage)
    console.log('Clearing localStorage keys:', keys)
    
    // Clear all localStorage
    localStorage.clear()
    
    console.log('✅ localStorage cleared successfully')
  } catch (error) {
    console.error('Failed to clear localStorage:', error)
  }
}

/**
 * Clear specific storage keys that might be causing quota issues
 */
export const clearProblematicStorageKeys = (): void => {
  try {
    const problematicKeys = [
      'nft-generator-app-store',
      'unified-memory-store',
      'nft_generator_rules',
      'nft_generator_layers',
      'nft_generator_projects',
      'nft_generator_current_project',
      'nft_generator_settings',
      'nft_generator_selected_traits'
    ]
    
    problematicKeys.forEach(key => {
      try {
        localStorage.removeItem(key)
        console.log(`Removed key: ${key}`)
      } catch (error) {
        console.warn(`Failed to remove key ${key}:`, error)
      }
    })
    
    console.log('✅ Problematic storage keys cleared')
  } catch (error) {
    console.error('Failed to clear problematic keys:', error)
  }
}

/**
 * Get storage usage information
 */
export const getStorageInfo = (): { used: number; keys: string[]; quota: number } => {
  try {
    let used = 0
    const keys = Object.keys(localStorage)
    
    keys.forEach(key => {
      try {
        const value = localStorage.getItem(key)
        if (value) {
          used += key.length + value.length
        }
      } catch (error) {
        console.warn(`Failed to get size for key ${key}:`, error)
      }
    })
    
    return {
      used,
      keys,
      quota: 5 * 1024 * 1024 // 5MB estimate
    }
  } catch (error) {
    console.error('Failed to get storage info:', error)
    return { used: 0, keys: [], quota: 0 }
  }
}

/**
 * Emergency storage cleanup - call this if quota is exceeded
 */
export const emergencyStorageCleanup = (): void => {
  console.log('🚨 Emergency storage cleanup initiated')
  
  const beforeInfo = getStorageInfo()
  console.log('Before cleanup:', beforeInfo)
  
  // Clear all localStorage
  clearAllLocalStorage()
  
  const afterInfo = getStorageInfo()
  console.log('After cleanup:', afterInfo)
  
  console.log('✅ Emergency cleanup completed')
}
