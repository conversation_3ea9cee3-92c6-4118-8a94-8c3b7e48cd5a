/**
 * Image Persistence Service
 * 
 * Trait thumbnail'larının refresh sonrası kaybolma sorununu ç<PERSON>
 * - File'ları IndexedDB'de data URL olarak saklar
 * - Refresh sonrası blob URL'leri yeniden oluşturur
 * - Memory-efficient caching strategy
 * - Automatic cleanup ve garbage collection
 */

import { useUnifiedMemory, createImageCacheKey, fileToImageCacheEntry } from './unified-memory.service';
import { unifiedStorageService } from '@/services/storage/unified-storage.service';

// ===== TYPES =====

interface TraitImageData {
  layerName: string;
  traitName: string;
  fileName: string;
  dataUrl: string;
  fileSize: number;
  lastAccessed: number;
}

interface ImagePersistenceConfig {
  maxCacheSize: number; // bytes
  maxAge: number; // milliseconds
  compressionQuality: number;
}

// ===== CONFIGURATION =====

const DEFAULT_CONFIG: ImagePersistenceConfig = {
  maxCacheSize: 50 * 1024 * 1024, // 50MB
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  compressionQuality: 0.8
};

// ===== SERVICE IMPLEMENTATION =====

class ImagePersistenceService {
  private config: ImagePersistenceConfig;
  private compressionCanvas: HTMLCanvasElement | null = null;

  constructor(config: ImagePersistenceConfig = DEFAULT_CONFIG) {
    this.config = config;
  }

  /**
   * Store trait image for persistence
   */
  async storeTraitImage(
    file: File,
    layerName: string,
    traitName: string
  ): Promise<string> {
    try {
      // Create cache key
      const cacheKey = createImageCacheKey(layerName, traitName, file.size);
      
      // Check if already cached
      const { getCachedImage, cacheImage } = useUnifiedMemory.getState();
      const existing = getCachedImage(cacheKey);
      
      if (existing && existing.blobUrl) {
        // Update last accessed time
        existing.timestamp = Date.now();
        cacheImage(cacheKey, existing);
        return existing.blobUrl;
      }

      // Convert file to cache entry
      const cacheEntry = await fileToImageCacheEntry(file, layerName, traitName);
      
      // Compress if needed
      const compressedDataUrl = await this.compressImageIfNeeded(
        cacheEntry.dataUrl,
        file.size
      );
      
      // Update cache entry with compressed data
      const finalEntry = {
        ...cacheEntry,
        dataUrl: compressedDataUrl
      };
      
      // Store in unified memory
      cacheImage(cacheKey, finalEntry);
      
      // Return blob URL for immediate use
      return finalEntry.blobUrl || cacheEntry.dataUrl;
      
    } catch (error) {
      console.error('Failed to store trait image:', error);
      // Fallback to direct blob URL
      return URL.createObjectURL(file);
    }
  }

  /**
   * Retrieve trait image (with automatic blob URL regeneration)
   */
  async getTraitImage(
    layerName: string,
    traitName: string,
    fileSize?: number
  ): Promise<string | null> {
    try {
      const cacheKey = createImageCacheKey(layerName, traitName, fileSize);
      const { getCachedImage, cacheImage } = useUnifiedMemory.getState();
      const cached = getCachedImage(cacheKey);
      
      if (!cached) {
        return null;
      }
      
      // Update last accessed time
      cached.timestamp = Date.now();
      
      // SECURITY FIX: Prefer data URLs over blob URLs to avoid security errors
      if (cached.dataUrl) {
        // Update last accessed time and return data URL directly
        cached.timestamp = Date.now();
        cacheImage(cacheKey, cached);
        return cached.dataUrl;
      }

      // Fallback: Check if blob URL is still valid (but convert to data URL)
      if (cached.blobUrl) {
        try {
          // Test if blob URL is still accessible
          await fetch(cached.blobUrl, { method: 'HEAD' });

          // Convert blob URL to data URL for security
          const response = await fetch(cached.blobUrl);
          const blob = await response.blob();
          const dataUrl = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = () => reject(reader.error);
            reader.readAsDataURL(blob);
          });

          // Update cache with data URL
          const updatedEntry = {
            ...cached,
            dataUrl,
            blobUrl: undefined, // Remove blob URL to prevent future security issues
            timestamp: Date.now()
          };

          cacheImage(cacheKey, updatedEntry);
          return dataUrl;
        } catch (e) {
          console.warn('Blob URL is invalid or inaccessible:', e);
          // Blob URL is invalid, cannot recover
        }
      }
      
      return null;
      
    } catch (error) {
      console.error('Failed to get trait image:', error);
      return null;
    }
  }

  /**
   * Batch store multiple trait images
   */
  async batchStoreTraitImages(
    files: { file: File; layerName: string; traitName: string }[]
  ): Promise<Map<string, string>> {
    const results = new Map<string, string>();
    
    // Process in chunks to avoid memory issues
    const chunkSize = 10;
    for (let i = 0; i < files.length; i += chunkSize) {
      const chunk = files.slice(i, i + chunkSize);
      
      const chunkPromises = chunk.map(async ({ file, layerName, traitName }) => {
        try {
          const blobUrl = await this.storeTraitImage(file, layerName, traitName);
          const key = `${layerName}/${traitName}`;
          results.set(key, blobUrl);
        } catch (error) {
          console.error(`Failed to store image for ${layerName}/${traitName}:`, error);
        }
      });
      
      await Promise.all(chunkPromises);
      
      // Small delay between chunks
      if (i + chunkSize < files.length) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }
    
    return results;
  }

  /**
   * Clean up old cached images
   */
  async cleanupOldImages(): Promise<void> {
    try {
      const { imageCache, clearImageCache } = useUnifiedMemory.getState();
      const now = Date.now();
      let totalSize = 0;
      const entries = Array.from(imageCache.entries());
      
      // Calculate total size and filter old entries
      const validEntries = entries.filter(([key, entry]) => {
        const age = now - entry.timestamp;
        const isValid = age < this.config.maxAge;
        
        if (isValid) {
          totalSize += entry.size;
        } else {
          // Revoke old blob URLs
          if (entry.blobUrl) {
            try {
              URL.revokeObjectURL(entry.blobUrl);
            } catch (e) {
              // Ignore errors
            }
          }
        }
        
        return isValid;
      });
      
      // If total size exceeds limit, remove oldest entries
      if (totalSize > this.config.maxCacheSize) {
        validEntries.sort((a, b) => a[1].timestamp - b[1].timestamp);
        
        while (totalSize > this.config.maxCacheSize && validEntries.length > 0) {
          const [key, entry] = validEntries.shift()!;
          totalSize -= entry.size;
          
          // Revoke blob URL
          if (entry.blobUrl) {
            try {
              URL.revokeObjectURL(entry.blobUrl);
            } catch (e) {
              // Ignore errors
            }
          }
        }
      }
      
      // Update cache with cleaned entries
      const newCache = new Map(validEntries);
      useUnifiedMemory.setState({ imageCache: newCache });
      
      console.log(`Image cache cleanup: ${entries.length - validEntries.length} entries removed`);
      
    } catch (error) {
      console.error('Failed to cleanup old images:', error);
    }
  }

  /**
   * Compress image if it's too large
   */
  private async compressImageIfNeeded(
    dataUrl: string,
    originalSize: number
  ): Promise<string> {
    // Only compress if larger than 1MB
    if (originalSize < 1024 * 1024) {
      return dataUrl;
    }
    
    try {
      if (!this.compressionCanvas) {
        this.compressionCanvas = document.createElement('canvas');
      }
      
      const canvas = this.compressionCanvas;
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        return dataUrl;
      }
      
      // Load image
      const img = new Image();
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = dataUrl;
      });
      
      // Calculate new dimensions (max 800px)
      const maxDimension = 800;
      let { width, height } = img;
      
      if (width > maxDimension || height > maxDimension) {
        const ratio = Math.min(maxDimension / width, maxDimension / height);
        width *= ratio;
        height *= ratio;
      }
      
      // Draw compressed image
      canvas.width = width;
      canvas.height = height;
      ctx.drawImage(img, 0, 0, width, height);
      
      // Return compressed data URL
      return canvas.toDataURL('image/jpeg', this.config.compressionQuality);
      
    } catch (error) {
      console.error('Failed to compress image:', error);
      return dataUrl;
    }
  }

  /**
   * Convert data URL to blob
   */
  private async dataUrlToBlob(dataUrl: string): Promise<Blob> {
    const response = await fetch(dataUrl);
    return response.blob();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    entryCount: number;
    totalSize: number;
    oldestEntry: number;
    newestEntry: number;
  } {
    const { imageCache } = useUnifiedMemory.getState();
    const entries = Array.from(imageCache.values());
    
    if (entries.length === 0) {
      return {
        entryCount: 0,
        totalSize: 0,
        oldestEntry: 0,
        newestEntry: 0
      };
    }
    
    const totalSize = entries.reduce((sum, entry) => sum + entry.size, 0);
    const timestamps = entries.map(entry => entry.timestamp);
    
    return {
      entryCount: entries.length,
      totalSize,
      oldestEntry: Math.min(...timestamps),
      newestEntry: Math.max(...timestamps)
    };
  }
}

// ===== SINGLETON INSTANCE =====

export const imagePersistenceService = new ImagePersistenceService();

// ===== UTILITY FUNCTIONS =====

/**
 * Initialize image persistence system
 */
export const initializeImagePersistence = async () => {
  // Clean up old images on startup
  await imagePersistenceService.cleanupOldImages();
  
  // Set up periodic cleanup (every hour)
  setInterval(() => {
    imagePersistenceService.cleanupOldImages();
  }, 60 * 60 * 1000);
};

/**
 * Store trait image with automatic persistence
 */
export const storeTraitImage = (
  file: File,
  layerName: string,
  traitName: string
): Promise<string> => {
  return imagePersistenceService.storeTraitImage(file, layerName, traitName);
};

/**
 * Get trait image with automatic blob URL regeneration
 */
export const getTraitImage = (
  layerName: string,
  traitName: string,
  fileSize?: number
): Promise<string | null> => {
  return imagePersistenceService.getTraitImage(layerName, traitName, fileSize);
};

export default imagePersistenceService;
