import React, { useState } from 'react'
import {
  Box,
  Typography,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Chip,
  Stack,
  CircularProgress,
  Alert,
  Collapse
} from '@mui/material'
import {
  FolderOpenRounded,
  LayersRounded,
  VisibilityRounded,
  VisibilityOffRounded,
  LockRounded,
  LockOpenRounded,
  DragIndicatorRounded,
  ExpandMoreRounded,
  ExpandLessRounded,
  DeleteRounded,
  ContentCopyRounded,
  EditRounded
} from '@mui/icons-material'
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable'
import {
  useSortable
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { useAppStore } from '../../../application/stores/appStore'
import { Layer } from '../../../domain/entities/Layer'
import { ContextMenu, useContextMenu, ContextMenuItem } from '../common/ContextMenu'
import { RenameDialog } from '../common/RenameDialog'
import { ImportOptionsDialog, ImportOptions } from '../common/ImportOptionsDialog'
import { TraitGroupItem } from './TraitGroupItem'
import { EditableRarityChip } from './EditableRarityChip'
import { PanelHeader, LayersPanelActions } from '../panels/PanelHeader'
import { analyzeImportedFiles } from '../../../infrastructure/services/content-analysis/simple-relationship-analyzer'
import { useContentAnalysis } from '../../contexts/ContentAnalysisContext'
import { toast } from 'react-hot-toast'

interface LayersPanelProps {
  className?: string
}

interface SortableLayerItemProps {
  layer: Layer
  isSelected: boolean
  isExpanded: boolean
  expandedLayers: Set<string>
  expandedGroups: Set<string>
  depth?: number
  onSelect: () => void
  onToggleExpand: () => void
  onToggleVisibility: () => void
  onToggleLock: () => void
  onContextMenu: (event: React.MouseEvent, layer: Layer) => void
  onToggleGroupExpand: (groupId: string) => void
  onToggleGroupVisibility: (groupId: string) => void
  onUpdateGroupRarityConstraint: (groupId: string, constraint: any) => void
  onTraitGroupSelect: (groupId: string) => void
  // Functions for child layers
  handleLayerSelect: (layerId: string) => void
  handleToggleExpand: (layerId: string) => void
  toggleLayerVisibility: (layerId: string) => void
  toggleLayerLock: (layerId: string) => void
  setLayerRarityConstraint: (layerId: string, maxRarity: number, isLocked: boolean) => void
  uiState: any
}

const SortableLayerItem: React.FC<SortableLayerItemProps> = ({
  layer,
  isSelected,
  isExpanded,
  expandedLayers,
  expandedGroups,
  depth = 0,
  onSelect,
  onToggleExpand,
  onToggleVisibility,
  onToggleLock,
  onContextMenu,
  onToggleGroupExpand,
  onToggleGroupVisibility,
  onUpdateGroupRarityConstraint,
  onTraitGroupSelect,
  handleLayerSelect,
  handleToggleExpand,
  toggleLayerVisibility,
  toggleLayerLock,
  setLayerRarityConstraint,
  uiState
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: layer.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1
  }

  // Calculate layer rarity (always 100% since each layer contributes one trait)
  const calculateLayerRarity = (layer: any) => {
    // Layer rarity is always 100% because each layer contributes exactly one trait to the NFT
    // The individual trait rarities within the layer determine which trait gets selected
    return layer.traits && layer.traits.length > 0 ? 100 : 0
  }

  const hasChildren = layer.children && layer.children.length > 0

  // Improved trait groups detection - more reliable across browsers
  const hasTraitGroups = Boolean(
    (layer.hasTraitGroups && layer.hasTraitGroups()) ||
    (layer.traitGroups && layer.traitGroups.length > 0) ||
    (layer.traitGroups && Object.keys(layer.traitGroups || {}).length > 0)
  )



  const shouldShowAccordion = hasChildren || hasTraitGroups

  return (
    <React.Fragment>
      <ListItem
        ref={setNodeRef}
        style={style}
        sx={{
          pl: 2 + depth * 2,
          borderBottom: '1px solid',
          borderColor: 'divider',
          bgcolor: isSelected ? 'action.selected' : 'transparent',
          '&:hover': {
            bgcolor: isSelected ? 'action.selected' : 'action.hover'
          },
          cursor: 'pointer'
        }}
        onClick={onSelect}
        onContextMenu={(e) => onContextMenu(e, layer)}
      >
        {/* Drag Handle */}
        <ListItemIcon
          sx={{ minWidth: 24 }}
          {...attributes}
          {...listeners}
        >
          <DragIndicatorRounded sx={{ fontSize: 16, color: 'text.disabled', cursor: 'grab' }} />
        </ListItemIcon>

        {/* Expand/Collapse Button - Improved Logic */}
        {shouldShowAccordion ? (
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation()
              onToggleExpand()
            }}
            sx={{ mr: 1 }}
          >
            {isExpanded ? (
              <ExpandLessRounded sx={{ fontSize: 16 }} />
            ) : (
              <ExpandMoreRounded sx={{ fontSize: 16 }} />
            )}
          </IconButton>
        ) : (
          <Box sx={{ width: 32 }} />
        )}

        {/* Layer Icon */}
        <ListItemIcon sx={{ minWidth: 32 }}>
          <LayersRounded sx={{ fontSize: 16 }} />
        </ListItemIcon>

        {/* Layer Info */}
        <ListItemText
          primary={layer.name}
          secondary={
            (layer.traitGroups && layer.traitGroups.length > 0)
              ? `${layer.traitGroups?.length || 0} groups, ${layer.traits?.length || 0} traits`
              : `${layer.traits?.length || 0} traits`
          }
          primaryTypographyProps={{
            fontSize: '13px',
            fontWeight: isSelected ? 600 : 500,
            color: isSelected ? 'primary.main' : 'text.primary'
          }}
          secondaryTypographyProps={{
            fontSize: '11px'
          }}
        />

        {/* Layer Controls */}
        <Stack direction="row" spacing={0.5} onClick={(e) => e.stopPropagation()}>
          {/* Editable Rarity Chip */}
          <EditableRarityChip
            layerId={layer.id}
            layerName={layer.name}
            currentRarity={layer.rarityConstraint?.maxRarity || calculateLayerRarity(layer)}
            isLocked={layer.rarityConstraint?.isLocked || false}
            onUpdate={(layerId, maxRarity, isLocked) => {
              console.log(`🎯 EditableRarityChip onUpdate called:`, { layerId, maxRarity, isLocked })
              console.log(`🎯 setLayerRarityConstraint type:`, typeof setLayerRarityConstraint)
              if (setLayerRarityConstraint) {
                setLayerRarityConstraint(layerId, maxRarity, isLocked)
              } else {
                console.error('❌ setLayerRarityConstraint is not a function!')
              }
            }}
          />

          {/* Visibility Toggle */}
          <IconButton
            size="small"
            onClick={onToggleVisibility}
            sx={{
              color: layer.isVisible ? 'primary.main' : 'text.disabled'
            }}
          >
            {layer.isVisible ? (
              <VisibilityRounded sx={{ fontSize: 14 }} />
            ) : (
              <VisibilityOffRounded sx={{ fontSize: 14 }} />
            )}
          </IconButton>

          {/* Lock Toggle - Also controls rarity constraint */}
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation()
              if (process.env.NODE_ENV === 'development') {
                console.log(`🔒 Lock toggle clicked for layer: ${layer.name}`)
                console.log(`  Current isLocked: ${layer.isLocked}`)
                console.log(`  Current rarityConstraint:`, layer.rarityConstraint)
              }

              // Toggle both layer lock and rarity constraint lock
              onToggleLock()

              // If layer has rarity constraint, toggle its lock state too
              if (layer.rarityConstraint) {
                const currentRarity = layer.rarityConstraint.maxRarity
                const newLockState = !layer.isLocked
                if (setLayerRarityConstraint) {
                  setLayerRarityConstraint(layer.id, currentRarity, newLockState)
                }
              }
            }}
            sx={{
              color: (layer.isLocked || layer.rarityConstraint?.isLocked) ? 'warning.main' : 'text.disabled'
            }}
          >
            {(layer.isLocked || layer.rarityConstraint?.isLocked) ? (
              <LockRounded sx={{ fontSize: 14 }} />
            ) : (
              <LockOpenRounded sx={{ fontSize: 14 }} />
            )}
          </IconButton>
        </Stack>
      </ListItem>

      {/* Render Children */}
      {hasChildren && (
        <Collapse in={isExpanded} timeout="auto" unmountOnExit>
          {layer.children!.map(child => (
            <SortableLayerItem
              key={child.id}
              layer={child}
              isSelected={uiState.selectedLayerId === child.id}
              isExpanded={expandedLayers.has(child.id)}
              expandedLayers={expandedLayers}
              expandedGroups={expandedGroups}
              depth={depth + 1}
              onSelect={() => handleLayerSelect(child.id)}
              onToggleExpand={() => handleToggleExpand(child.id)}
              onToggleVisibility={() => toggleLayerVisibility(child.id)}
              onToggleLock={() => toggleLayerLock(child.id)}
              onContextMenu={onContextMenu}
              onToggleGroupExpand={onToggleGroupExpand}
              onToggleGroupVisibility={onToggleGroupVisibility}
              onUpdateGroupRarityConstraint={onUpdateGroupRarityConstraint}
              onTraitGroupSelect={onTraitGroupSelect}
              handleLayerSelect={handleLayerSelect}
              handleToggleExpand={handleToggleExpand}
              toggleLayerVisibility={toggleLayerVisibility}
              toggleLayerLock={toggleLayerLock}
              setLayerRarityConstraint={setLayerRarityConstraint}
              uiState={uiState}
            />
          ))}
        </Collapse>
      )}

      {/* Render TraitGroups - Improved Logic */}
      {hasTraitGroups && (
        <Collapse in={isExpanded} timeout="auto" unmountOnExit>
          {(layer.traitGroups || []).map(group => (
            <TraitGroupItem
              key={group.id}
              group={group}
              isExpanded={expandedGroups.has(group.id)}
              isSelected={uiState.selectedTraitGroup === group.id}
              depth={depth}
              onToggleExpand={() => onToggleGroupExpand(group.id)}
              onToggleVisibility={() => onToggleGroupVisibility(group.id)}
              onUpdateRarityConstraint={(constraint) => onUpdateGroupRarityConstraint(group.id, constraint)}
              onSelect={() => onTraitGroupSelect(group.id)}
            />
          ))}
        </Collapse>
      )}
    </React.Fragment>
  )
}

export const LayersPanel: React.FC<LayersPanelProps> = ({ className }) => {
  const {
    currentProject,
    isLoading,
    progress,
    errors,
    importLayers,
    toggleLayerVisibility,
    toggleLayerLock,
    setSelectedLayer,
    setSelectedTraitGroup,
    reorderLayers,
    deleteLayer,
    duplicateLayer,
    renameLayer,
    setLayerRarityConstraint,
    uiState
  } = useAppStore()



  const [expandedLayers, setExpandedLayers] = useState<Set<string>>(new Set())
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set())
  const [selectedLayerForContext, setSelectedLayerForContext] = useState<Layer | null>(null)
  const [renameDialog, setRenameDialog] = useState<{ open: boolean; layer: Layer | null }>({
    open: false,
    layer: null
  })
  const [importDialog, setImportDialog] = useState(false)

  const { contextMenu, handleContextMenu, handleClose } = useContextMenu()
  const contentAnalysis = useContentAnalysis()

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  const handleImportLayers = () => {
    setImportDialog(true)
  }

  const handleImportConfirm = async (options: ImportOptions) => {
    setImportDialog(false)

    try {
      const result = await importLayers({
        replaceExisting: options.replaceExisting
      })
      if (result.success) {
        console.log(`Successfully imported ${result.layers.length} layers`)

        // Trigger content analysis after successful import
        if (result.originalFiles) {
          setTimeout(() => {
            triggerRealContentAnalysis(result.originalFiles!)
          }, 500) // Small delay to ensure UI is updated
        } else {
          // Fallback to mock analysis if no original files
          setTimeout(() => {
            triggerContentAnalysisForImportedLayers(result.layers)
          }, 500)
        }
      }
    } catch (error) {
      console.error('Import failed:', error)
    }
  }

  const handleImportCancel = () => {
    setImportDialog(false)
  }

  // Function to trigger real content analysis using imported files
  const triggerRealContentAnalysis = (files: FileList) => {
    try {
      console.log('🔍 Starting REAL content analysis with FileList...')
      console.log(`📁 Analyzing ${files.length} files`)

      // Use the real analyzer
      const analysisResults = analyzeImportedFiles(files)

      console.log('📊 Real content analysis completed:', analysisResults)

      // Convert to expected format for dialog
      const formattedResults = {
        relationships: analysisResults.relationships.map(rel => ({
          type: 'keyword_match',
          confidence: 0.8,
          description: `Keyword "${rel.keyword}" found in ${rel.layers.length} layers`,
          layerIds: rel.layers.map(l => l.layerName),
          traitIds: []
        })),
        suggestions: analysisResults.suggestions.map(suggestion => ({
          type: 'trait_matching',
          confidence: suggestion.confidence,
          description: suggestion.description,
          ruleData: {
            triggerLayer: suggestion.triggerLayer,
            triggerValue: suggestion.triggerValue,
            targetLayer: suggestion.targetLayer,
            targetValues: suggestion.targetValues
          },
          priority: (suggestion.confidence > 0.7 ? 'high' : 'medium') as 'high' | 'medium' | 'low'
        })),
        traitFiles: analysisResults.traitFiles.map(file => ({
          layerId: file.layerName,
          traitId: file.fileName,
          filename: file.fileName,
          patterns: file.extractedKeywords
        })),
        timestamp: new Date().toISOString()
      }

      // Show analysis results dialog using the hook
      contentAnalysis.finishAnalysis(formattedResults)

      // Show toast notification about analysis results
      toast.success(`Content analysis complete! Found ${analysisResults.suggestions.length} rule suggestions.`, {
        duration: 4000
      })
    } catch (error) {
      console.error('Real content analysis failed:', error)
    }
  }

  // Function to trigger content analysis for imported layers (fallback)
  const triggerContentAnalysisForImportedLayers = (layers: any[]) => {
    try {
      // Create mock analysis results based on imported layers
      const traitFiles = layers.flatMap(layer =>
        (layer.traits || []).map((trait: any) => ({
          layerId: layer.id,
          traitId: trait.id,
          filename: trait.name + '.png',
          patterns: extractPatternsFromTraitName(trait.name)
        }))
      )

      const relationships = analyzeLayerRelationships(layers)
      const suggestions = generateRuleSuggestions(relationships)

      const analysisResults = {
        relationships,
        suggestions,
        traitFiles,
        timestamp: new Date().toISOString()
      }

      // Show analysis results dialog using the hook
      contentAnalysis.finishAnalysis(analysisResults)

      // Show toast notification about analysis results
      toast.success(`Content analysis complete! Found ${analysisResults.suggestions.length} rule suggestions.`, {
        duration: 4000
      })
    } catch (error) {
      console.error('Fallback content analysis failed:', error)
    }
  }

  // Helper function to extract patterns from trait names
  const extractPatternsFromTraitName = (name: string): string[] => {
    const patterns: string[] = []
    const lowerName = name.toLowerCase()

    // Common color patterns
    const colors = ['blue', 'red', 'green', 'yellow', 'black', 'white', 'brown', 'pink', 'purple', 'orange']
    colors.forEach(color => {
      if (lowerName.includes(color)) patterns.push(color)
    })

    // Common emotion patterns
    const emotions = ['happy', 'sad', 'angry', 'surprised', 'neutral', 'smile', 'frown']
    emotions.forEach(emotion => {
      if (lowerName.includes(emotion)) patterns.push(emotion)
    })

    return patterns
  }

  // Helper function to analyze relationships between layers
  const analyzeLayerRelationships = (layers: any[]) => {
    const relationships: any[] = []
    const patternMap = new Map<string, string[]>()

    // Collect patterns from all layers
    layers.forEach(layer => {
      (layer.traits || []).forEach((trait: any) => {
        const patterns = extractPatternsFromTraitName(trait.name)
        patterns.forEach(pattern => {
          if (!patternMap.has(pattern)) {
            patternMap.set(pattern, [])
          }
          patternMap.get(pattern)!.push(layer.name)
        })
      })
    })

    // Create relationships for patterns that appear in multiple layers
    patternMap.forEach((layerNames, pattern) => {
      if (layerNames.length > 1) {
        relationships.push({
          type: 'color_emotion_match',
          confidence: 0.8,
          description: `Pattern "${pattern}" found across multiple layers: ${layerNames.join(', ')}`,
          layerIds: layerNames,
          traitIds: []
        })
      }
    })

    return relationships
  }

  // Helper function to generate rule suggestions
  const generateRuleSuggestions = (relationships: any[]) => {
    return relationships.map(rel => ({
      type: 'trait_matching',
      confidence: rel.confidence,
      description: `Create rule to match traits with similar patterns across ${rel.layerIds.join(' and ')}`,
      ruleData: {
        triggerLayer: rel.layerIds[0],
        targetLayers: rel.layerIds.slice(1)
      },
      priority: (rel.confidence > 0.7 ? 'high' : 'medium') as 'high' | 'medium' | 'low'
    }))
  }

  const handleLayerSelect = (layerId: string) => {
    setSelectedLayer(layerId === uiState.selectedLayerId ? undefined : layerId)
  }

  const handleTraitGroupSelect = (groupId: string) => {
    // Set the selected trait group in UI state
    setSelectedTraitGroup(groupId === uiState.selectedTraitGroup ? undefined : groupId)
  }

  const handleToggleExpand = (layerId: string) => {
    const newExpanded = new Set(expandedLayers)
    if (newExpanded.has(layerId)) {
      newExpanded.delete(layerId)
    } else {
      newExpanded.add(layerId)
    }
    setExpandedLayers(newExpanded)
  }

  const handleToggleGroupExpand = (groupId: string) => {
    const newExpanded = new Set(expandedGroups)
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId)
    } else {
      newExpanded.add(groupId)
    }
    setExpandedGroups(newExpanded)
  }

  const handleToggleGroupVisibility = (groupId: string) => {
    const { currentProject } = useAppStore.getState()
    if (!currentProject) return

    // Find the group in any layer
    for (const layer of currentProject.layers) {
      const group = layer.getTraitGroup?.(groupId)
      if (group) {
        group.isVisible = !group.isVisible
        // Update store to trigger re-render
        useAppStore.setState({ currentProject })
        break
      }
    }
  }

  const handleUpdateGroupRarityConstraint = (groupId: string, constraint: any) => {
    const { currentProject } = useAppStore.getState()
    if (!currentProject) return

    // Find the group in any layer
    for (const layer of currentProject.layers) {
      const group = layer.getTraitGroup?.(groupId)
      if (group) {
        group.updateRarityConstraint(constraint)
        // Update store to trigger re-render
        useAppStore.setState({ currentProject })
        break
      }
    }
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (active.id !== over?.id && over?.id) {
      const layers = currentProject?.layers || []
      const sortedLayers = [...layers].sort((a, b) => (a.order || 0) - (b.order || 0))

      const oldIndex = sortedLayers.findIndex(layer => layer.id === active.id)
      const newIndex = sortedLayers.findIndex(layer => layer.id === over.id)

      if (oldIndex !== -1 && newIndex !== -1) {
        const newLayers = arrayMove(sortedLayers, oldIndex, newIndex)

        // Update order values based on new positions
        newLayers.forEach((layer, index) => {
          layer.order = index
        })

        // Trigger store update
        const layerIds = newLayers.map(layer => layer.id)
        reorderLayers(layerIds)
      }
    }
  }

  const handleLayerContextMenu = (event: React.MouseEvent, layer: Layer) => {
    setSelectedLayerForContext(layer)
    handleContextMenu(event)
  }

  const handleDeleteLayer = () => {
    if (selectedLayerForContext) {
      deleteLayer(selectedLayerForContext.id)
      setSelectedLayerForContext(null)
    }
  }

  const handleDuplicateLayer = () => {
    if (selectedLayerForContext) {
      duplicateLayer(selectedLayerForContext.id)
      setSelectedLayerForContext(null)
    }
  }

  const handleRenameLayer = () => {
    if (selectedLayerForContext) {
      setRenameDialog({ open: true, layer: selectedLayerForContext })
      setSelectedLayerForContext(null)
    }
  }

  const handleRenameConfirm = (newName: string) => {
    if (renameDialog.layer) {
      renameLayer(renameDialog.layer.id, newName)
    }
    setRenameDialog({ open: false, layer: null })
  }

  const handleRenameCancel = () => {
    setRenameDialog({ open: false, layer: null })
  }

  const contextMenuItems: ContextMenuItem[] = [
    {
      id: 'rename',
      label: 'Rename',
      icon: <EditRounded sx={{ fontSize: 16 }} />,
      onClick: handleRenameLayer
    },
    {
      id: 'duplicate',
      label: 'Duplicate',
      icon: <ContentCopyRounded sx={{ fontSize: 16 }} />,
      onClick: handleDuplicateLayer
    },
    {
      id: 'delete',
      label: 'Delete',
      icon: <DeleteRounded sx={{ fontSize: 16 }} />,
      danger: true,
      divider: true,
      onClick: handleDeleteLayer
    }
  ]



  const layers = currentProject?.layers || []
  const sortedLayers = [...layers].sort((a, b) => (a.order || 0) - (b.order || 0))

  return (
    <Box className={className} sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      {/* Panel Header */}
      <PanelHeader
        title="Layers"
        subtitle={layers.length > 0 ? `${layers.length} layers` : undefined}
        actions={
          <LayersPanelActions
            onImport={handleImportLayers}
            layerCount={layers.length}
            isLoading={isLoading}
          />
        }
      />

      {/* Progress Indicator */}
      {isLoading && progress && (
        <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Stack direction="row" spacing={2} alignItems="center">
            <CircularProgress size={20} />
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="body2" sx={{ fontSize: '12px' }}>
                {progress.message}
              </Typography>
              {progress.total > 0 && (
                <Typography variant="caption" sx={{ fontSize: '10px', color: 'text.secondary' }}>
                  {progress.current} / {progress.total}
                </Typography>
              )}
            </Box>
          </Stack>
        </Box>
      )}

      {/* Error Messages */}
      {errors.length > 0 && (
        <Box sx={{ p: 1 }}>
          {errors.slice(-1).map(error => (
            <Alert key={error.id} severity="error" sx={{ fontSize: '12px' }}>
              {error.message}
            </Alert>
          ))}
        </Box>
      )}

      {/* Layers List */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {layers.length === 0 ? (
          <Box
            sx={{
              p: 3,
              textAlign: 'center',
              color: 'text.secondary'
            }}
          >
            <LayersRounded sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
            <Typography variant="body2" sx={{ mb: 2 }}>
              No layers imported yet
            </Typography>
            <Button
              variant="outlined"
              size="small"
              startIcon={<FolderOpenRounded />}
              onClick={handleImportLayers}
              disabled={isLoading}
            >
              Import Layers
            </Button>
          </Box>
        ) : (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={sortedLayers.map(layer => layer.id)}
              strategy={verticalListSortingStrategy}
            >
              <List dense sx={{ p: 0 }}>
                {sortedLayers.map(layer => (
                  <SortableLayerItem
                    key={layer.id}
                    layer={layer as any}
                    isSelected={uiState.selectedLayerId === layer.id}
                    isExpanded={expandedLayers.has(layer.id)}
                    expandedLayers={expandedLayers}
                    expandedGroups={expandedGroups}
                    onSelect={() => handleLayerSelect(layer.id)}
                    onToggleExpand={() => handleToggleExpand(layer.id)}
                    onToggleVisibility={() => toggleLayerVisibility(layer.id)}
                    onToggleLock={() => toggleLayerLock(layer.id)}
                    onContextMenu={handleLayerContextMenu}
                    onToggleGroupExpand={handleToggleGroupExpand}
                    onToggleGroupVisibility={handleToggleGroupVisibility}
                    onUpdateGroupRarityConstraint={handleUpdateGroupRarityConstraint}
                    onTraitGroupSelect={handleTraitGroupSelect}
                    handleLayerSelect={handleLayerSelect}
                    handleToggleExpand={handleToggleExpand}
                    toggleLayerVisibility={toggleLayerVisibility}
                    toggleLayerLock={toggleLayerLock}
                    setLayerRarityConstraint={setLayerRarityConstraint}
                    uiState={uiState}
                  />
                ))}
              </List>
            </SortableContext>
          </DndContext>
        )}
      </Box>

      {/* Context Menu */}
      <ContextMenu
        items={contextMenuItems}
        anchorPosition={contextMenu.position}
        open={contextMenu.open}
        onClose={handleClose}
      />

      {/* Rename Dialog */}
      <RenameDialog
        open={renameDialog.open}
        title="Rename Layer"
        currentName={renameDialog.layer?.name || ''}
        onConfirm={handleRenameConfirm}
        onCancel={handleRenameCancel}
      />

      {/* Import Options Dialog */}
      <ImportOptionsDialog
        open={importDialog}
        hasExistingLayers={(currentProject?.layers?.length || 0) > 0}
        browserSupportsDirectoryPicker={'showDirectoryPicker' in window}
        onConfirm={handleImportConfirm}
        onCancel={handleImportCancel}
      />
    </Box>
  )
}
